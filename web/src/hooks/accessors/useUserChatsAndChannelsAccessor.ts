import React from 'react';
import { WeakTokenProvider } from '../../types/TokenProvider';
import {
  initGraphClient,
} from './useGraphApiAccessor';

// エラー定数
export const UseUserChatsAndChannelsError = {
  TOKEN_PROVIDER_NOT_AVAILABLE: 'TOKEN_PROVIDER_NOT_AVAILABLE',
  FETCH_CHATS_FAILED: 'FETCH_CHATS_FAILED',
  FETCH_TEAMS_FAILED: 'FETCH_TEAMS_FAILED',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
};

// GraphAPIから取得する値の型
// 表示に必要な型
export interface IUserChatItem {
  // chatId or ChannelId
  id: string;
  // 記事名
  name: string;
  // 内部処理で必要なtype(不要かも)
  type: 'チャット' | 'チャネル';
  // APIから帰ってくるchatType
  chatType: 'oneOnOne' | 'group' | 'meeting' |'TeamsChannel'; // チャットの場合はMicrosoft Graph APIのchatType、チャネルの場合は'TeamsChannel'
  // チャネルの場合のチームID(持っていて欲しい)
  teamId?: string;
}

export interface IUserChat {
  id: string;
  topic: string | null;
  chatType: string;
  members?: {
    displayName: string;
    id: string;
  }[];
}

export interface IUserTeam {
  id: string;
  displayName: string;
  channels?: IUserChannel[];
}

export interface IUserChannel {
  id: string;
  displayName: string;
  membershipType: string;
}

export type FetchUserChatsAndChannels = () => Promise<IUserChatItem[]>;

export type UseUserChatsAndChannelsReturnType = {
  fetchUserChatsAndChannels: FetchUserChatsAndChannels | undefined;
  isLoading: boolean;
  error: string | null;
}

/**
 * ユーザーが参加しているチャットの一覧を取得する
 */
async function fetchUserChatsImpl(
  tokenProvider: WeakTokenProvider,
): Promise<IUserChat[]> {
  const client = initGraphClient(tokenProvider);
  if (!client) {
    return Promise.reject(new Error(UseUserChatsAndChannelsError.TOKEN_PROVIDER_NOT_AVAILABLE));
  }

  try {
    const response = await client
      .api('/me/chats')
      .expand('members')
      .orderby('lastMessagePreview/createdDateTime desc')
      .get();

    return response.value || [];
  } catch (error) {
    throw new Error(UseUserChatsAndChannelsError.FETCH_CHATS_FAILED);
  }
}

/**
 * ユーザーが参加しているチームとチャネルの一覧を取得する
 */
// async function fetchUserTeamsAndChannelsImpl(
//   tokenProvider: WeakTokenProvider,
// ): Promise<IUserTeam[]> {
//   const client = initGraphClient(tokenProvider);
//   if (!client) {
//     return Promise.reject(new Error(UseUserChatsAndChannelsError.TOKEN_PROVIDER_NOT_AVAILABLE));
//   }

//   try {
//     // まずユーザーが参加しているチームを取得
//     const teamsResponse = await client
//       .api('/me/joinedTeams')
//       .get();

//     const teams: IUserTeam[] = teamsResponse.value || [];

//     // 各チームのチャネルを取得
//     const teamsWithChannels = await Promise.all(
//       teams.map(async (team) => {
//         try {
//           const channelsResponse = await client
//             .api(`/teams/${team.id}/channels`)
//             .get();
//           return { ...team, channels: channelsResponse.value || [] };
//         } catch (error) {
//           return { ...team, channels: [] };
//         }
//       }),
//     );

//     return teamsWithChannels;
//   } catch (error) {
//     throw new Error(UseUserChatsAndChannelsError.FETCH_TEAMS_FAILED);
//   }
// }

/**
 * チャットとチャネルを統合してIUserChatItem形式に変換する
 * chatTypeをチャネルとチャットで振り分け
 */
function convertToUserChatItems(
  // APIから取得したchatItems
  chatItems: IUserChat[],
  // APIから取得したteamsItems(teamsの下にchannelがある)
  // teams: IUserTeam[],
): IUserChatItem[] {
  // 空のitemsを作成
  const items: IUserChatItem[] = [];
  // チャットを追加
  chatItems.forEach((chatItem) => {
    let name = chatItem.topic || 'チャット';

    // トピックがない場合はメンバー名を使用
    if (!chatItem.topic && chatItem.members && chatItem.members.length > 0) {
      const memberNames = chatItem.members
        .map((member) => member.displayName)
        .filter((memberName) => memberName)
        .join(', ');
      name = memberNames || 'チャット';
    }

    items.push({
      id: chatItem.id,
      name,
      type: 'チャット',
      chatType: chatItem.chatType as 'oneOnOne' | 'group' | 'meeting', // Microsoft Graph APIのchatTypeを保持
    });
  });

  // チャネルを追加
  // teams.forEach((team) => {
  //   if (team.channels) {
  //     team.channels.forEach((channel) => {
  //       items.push({
  //         id: channel.id,
  //         name: `${team.displayName} - ${channel.displayName}`,
  //         type: 'チャネル',
  //         // チャネルのchatTypeはTeamsChannelで固定
  //         chatType: 'TeamsChannel',
  //         // チャネルの場合はteamIdを設定
  //         teamId: team.id,
  //       });
  //     });
  //   }
  // });

  return items;
}

/**
 * ユーザーのチャットとチャネルを取得するカスタムフック
 */
const useUserChatsAndChannelsAccessor = (
  tokenProvider: WeakTokenProvider,
): UseUserChatsAndChannelsReturnType => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const fetchUserChatsAndChannels = React.useCallback(async (): Promise<IUserChatItem[]> => {
    if (!tokenProvider) {
      throw new Error(UseUserChatsAndChannelsError.TOKEN_PROVIDER_NOT_AVAILABLE);
    }

    setIsLoading(true);
    setError(null);

    try {
      // チャットとチーム(チャネル)を並行して取得
      const [chats] = await Promise.all([
        fetchUserChatsImpl(tokenProvider),
        // fetchUserTeamsAndChannelsImpl(tokenProvider),
      ]);
      // chatTypeでチャネルとチャットで振り分け
      const items = convertToUserChatItems(chats);

      setIsLoading(false);
      return items;
    } catch (err) {
      const errorMessage = err instanceof Error
        ? err.message
        : UseUserChatsAndChannelsError.UNKNOWN_ERROR;
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  }, [tokenProvider]);

  return {
    fetchUserChatsAndChannels: tokenProvider ? fetchUserChatsAndChannels : undefined,
    isLoading,
    error,
  };
};

export default useUserChatsAndChannelsAccessor;
