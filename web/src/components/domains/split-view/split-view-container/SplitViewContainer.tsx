import * as React from 'react';
import { useCustomCompareEffect } from 'react-use';
import { UserContext } from './UserContext';
import useSharePointApiAccessor from '../../../../hooks/accessors/useSharePointApiAccessor';
import SplitViewDetail, { SplitViewDetailMessage } from '../split-view-detail/SplitViewDetail';
import SplitViewList, { SEARCH_RESULT_MAX_COUNT, SplitViewListMessage, SplitViewListView } from '../split-view-list/SplitViewList';
import MessageToaster from '../../../commons/molecules/message-toaster/MessageToaster';
import useComponentInitUtility from '../../../../hooks/utilities/useComponentInitUtility';
import useSplitViewReducers from './hooks/useSplitViewReducers';
import useInitialDisplay from './hooks/useInitialDisplay';
import useSplitViewDetail from './hooks/useSplitViewDetail';
import useSplitViewHighlighter from './hooks/useSplitViewHighlighter';
import useSearchResultList from './hooks/useSearchResultList';
import useSearchRequestApiAccessor from '../../../../hooks/accessors/useSearchRequestApiAccessor';
import useIndexedDbAccessor from '../../../../hooks/accessors/useIndexedDbAccessor';
import useBookmarkRepositoryAccessor from '../../../../hooks/accessors/useBookmarkRepositoryAccessor';
import useBookmarksApiAccessor from '../../../../hooks/accessors/useBookmarksApiAccessor';
import useMessageToasterBehavior from '../../../../hooks/behaviors/useMessageToasterBehavior';
import useRemoteBookmarkFeature from '../../../../hooks/features/useRemoteBookmarkFeature';
import useBookmarkButtonBehavior from '../../../../hooks/behaviors/useBookmarkButtonBehavior';
import useBookmarksList from './hooks/useBookmarksList';
import useSearchResultRepositoryAccessor from '../../../../hooks/accessors/useSearchResultRepositoryAccessor';
import useSortButtonBehavior from '../../../../hooks/behaviors/useSortButtonBehavior';
import AppInfoMenu from '../../app-info/app-info-menu/AppInfoMenu';
import useAppInfo from '../../app-info/hooks/useAppInfo';
import useMailApiAccessor from '../../../../hooks/accessors/useMailApiAccessor';
import useChatApiAccessor from '../../../../hooks/accessors/useChatApiAccessor';
import useUserApiAccessor from '../../../../hooks/accessors/useUserApiAccessor';
import useChatReactionBehavior from '../../../../hooks/behaviors/useChatReactionBehavior';
import useCancellationButtonBehavior from '../../../../hooks/behaviors/useCancellationButtonBehavior';
import useGraphApiRequestQueue from '../../../../hooks/behaviors/useGraphApiRequestQueue';
import { IRequestPayload, bulkResponseConverter, sendRequests } from '../../../../hooks/accessors/useGraphApiAccessor';
import environment from '../../../../utilities/environment';
import { Message } from '../../../commons/molecules/search-input/SearchInput';
import useModal from '../../app-info/hooks/useModel';
import TeamsSettingModal from '../../app-info/teamsSettingModal/TeamsSettingModal';
import useTeamsChatsApiAccessor from '../../../../hooks/accessors/useTeamsChatsApiAccessor';

// TODO: 環境毎の設定
const AppTitle = 'atTane';

// トースターの表示ミリ秒
const MESSAGE_TOASTER_DURATION = 3000;

/**
 * a container component of the SplitView domain
 * @constructor
 */
const SplitViewContainer: React.FC = () => {
  const {
    initialDisplayReducerReturn,
    bookmarksListReducerReturn,
    // stateが空の配列を持っている
    searchResultListReducerReturn,
    // chat用のstate保持
    chatModeListReducerReturn,
    formedBookmarksList,
    // 整形されたsearchResultListReducerReturn
    formedSearchResultsList,
    // チャットモードのフォーマット済みリスト
    formedChatModeList,
    bookmarksEmptyFilterOptions,
    searchResultEmptyFilterOptions,
    // チャットモード用のフィルターオプション
    chatModeEmptyFilterOptions,
    activeBookmarkItemId,
    activeSearchResultItemId,
    // チャットモードのアクティブアイテムID
    activeChatModeItemId,
    listModeStateReturn,
    searchRequestStateReturn,
    syncBookmarkStateReturn,
    isInitialDisplayMode,
    isBookmarksListMode,
    isSearchListMode,
    // 検索モードの管理
    searchModeStateReturn,
    isChatSearchMode,
    isDetailOpen,
  } = useSplitViewReducers();
  const [initialDisplayState, dispatchInitialDisplay] = initialDisplayReducerReturn;
  const [bookmarksState, dispatchBookmarks] = bookmarksListReducerReturn;
  // dispatchSearchResultsが渡ってきている
  const [searchResultsState, dispatchSearchResults] = searchResultListReducerReturn;
  const [chatModeState, dispatchChatMode] = chatModeListReducerReturn;
  // 各モード間で持ち回るState
  const [listMode, setListMode] = listModeStateReturn;
  // 各検索モードで持ち回るState
  const [searchMode, setSearchMode] = searchModeStateReturn;
  const [searchRequest] = searchRequestStateReturn;
  const [, setSyncBookmark] = syncBookmarkStateReturn;

  // チャットメッセージの状態管理
  const [chatMessages, setChatMessages] = React.useState<Message[]>([
    { sender: 'atTane', text: 'お手伝いできることはありますか？' },
  ]);

  // チャットメッセージを追加する関数
  const addChatMessage = React.useCallback((message: Message) => {
    setChatMessages((prev) => [...prev, message]);
  }, []);

  // common component features
  const useComponentInitResults = useComponentInitUtility({
    componentName: 'SplitViewContainer',
    initLogConf: [initialDisplayReducerReturn[0].listView, SplitViewListView],
  });
  const [, reporters, tokens, , , oid, teamsToken, metrics] = useComponentInitResults;
  const [reportEvent] = reporters;
  const [, setPerformanceMetrics] = metrics;

  // get token providers
  const spoTokenProvider = React.useMemo(() => tokens?.get('spo'), [tokens]);
  const graphTokenProvider = React.useMemo(() => tokens?.get('graph'), [tokens]);
  const apiTokenProvider = React.useMemo(() => {
    if (!teamsToken) return undefined;
    return () => Promise.resolve(teamsToken);
  }, [teamsToken]);

  // init API accessors
  const cancellationRef = React.useRef(false);
  const useSharePointApiAccessorReturn = useSharePointApiAccessor(
    spoTokenProvider,
    cancellationRef,
  );
  const useMailApiReturn = useMailApiAccessor(graphTokenProvider);
  const useChatApiReturn = useChatApiAccessor(graphTokenProvider);
  const useUserApiReturn = useUserApiAccessor(graphTokenProvider);
  const {
    fetchDisplayNames,
    groupUsersByReaction,
  } = useChatReactionBehavior(graphTokenProvider, cancellationRef, reportEvent);
  const useGraphApiRequestQueueReturn = useGraphApiRequestQueue(
    cancellationRef, graphTokenProvider,
  );

  const useSearchRequestApiAccessorReturn = useSearchRequestApiAccessor(
    apiTokenProvider,
    reporters,
  );
  const {
    updateRemoteContextApi: updateRemoteContext,
    cancelSearchRequestApi,
  } = useSearchRequestApiAccessorReturn;

  const useBookmarkApiAccessorReturn = useBookmarksApiAccessor(apiTokenProvider);

  // Teams設定を保存するためのアクセサー
  // TokenはuIdを取得するために必要
  // reportはログ出力のため
  const useTeamsChatsApiAccessorReturn = useTeamsChatsApiAccessor(
    apiTokenProvider,
    reporters,
  );

  // IndexedDB features
  const [openDB] = useIndexedDbAccessor();

  // SPO記事のローカルリポジトリ
  const useSearchResultRepositoryResults = useSearchResultRepositoryAccessor(
    dispatchSearchResults,
    openDB,
  );
  const {
    isTransactionPending: isTransactionPendingResultsRepos,
    replaceCacheContext,
  } = useSearchResultRepositoryResults;

  /**
   * 検索結果画面コンテキストの更新
   * StateのContext更新をトリガーにCacheContext、RemoteContextをそれぞれ更新する
   */
  const dropTimeStamp = (key: string, value: unknown) => {
    if (key === 'timestamp') return undefined;
    return value;
  };
  const [isSearchResultContextUpdated, setIsSearchResultContextUpdated] = React.useState(false);
  const newSearchResultContext = React.useMemo(
    () => searchResultsState.context,
    [searchResultsState.context],
  );
  useCustomCompareEffect(
    () => {
      const updateContext = async () => {
        if (!newSearchResultContext) return;
        // CacheContextの更新
        if (!replaceCacheContext) return;
        const ids = formedSearchResultsList
          .slice(0, SEARCH_RESULT_MAX_COUNT + 1)
          .map((item) => item.id);
        await replaceCacheContext({ ...newSearchResultContext, displayIds: ids });

        // RemoteContextの更新
        if (!updateRemoteContext) return;
        if (!isSearchResultContextUpdated) return;
        updateRemoteContext(newSearchResultContext);
      };

      updateContext();
    },
    [
      newSearchResultContext,
      replaceCacheContext,
      updateRemoteContext,
      isSearchResultContextUpdated,
    ],
    (prev, next) => JSON.stringify(prev[0], dropTimeStamp)
      === JSON.stringify(next[0], dropTimeStamp),
  );

  // お気に入りのローカルリポジトリ
  const useBookmarkRepositoryResults = useBookmarkRepositoryAccessor(openDB);
  const {
    isTransactionPending: isTransactionPendingBookmarkRepos,
    bookmarkDict,
  } = useBookmarkRepositoryResults;

  const fetchItems = React.useMemo(
    () => function fetchImpl<T>(
      allRequests: IRequestPayload[], converter?: bulkResponseConverter<T>,
    ) {
      const neverCancel = { current: false };
      if (!graphTokenProvider) return undefined;
      return sendRequests(graphTokenProvider, allRequests, neverCancel, undefined, converter);
    }, [graphTokenProvider],
  );

  // リモート上のAPIとブラウザローカルのお気に入りデータの追加/削除/同期処理をする機能
  const useRemoteBookmarkResults = useRemoteBookmarkFeature(
    useBookmarkRepositoryResults,
    useBookmarkApiAccessorReturn,
    syncBookmarkStateReturn,
    fetchItems,
    reportEvent,
  );

  // a message toaster on the list
  const listMessageToasterBehaviorReturn = useMessageToasterBehavior(MESSAGE_TOASTER_DURATION);
  const [
    isListPopupShown,
    listPopupMessage,
    extendListPopupTimer,
  ] = listMessageToasterBehaviorReturn;

  // アプリ情報
  const [
    [
      appInfoOpen,
      appInfoView,
      appInfoMessages,
      appAIInfoMessages,
      appInfoErrorMessage,
    ],
    showAppInfo,
    hideAppInfo,
  ] = useAppInfo(reportEvent);

  // AISearch Teams検索用モーダル関数
  const [modalOpen, showModal, hideModal] = useModal();

  // keyword highlighting
  const {
    setSearchWords,
    showListHighlight,
    hideHighlight,
    showDetailHighlight,
    hideDetailHighlight,
  } = useSplitViewHighlighter();

  // 一覧上のお気に入りボタンの振る舞い
  const [onClickBookmarkOnList] = useBookmarkButtonBehavior(
    useComponentInitResults, useRemoteBookmarkResults, listMessageToasterBehaviorReturn,
  );

  // a message toaster on the detail view
  const detailMessageToasterBehaviorReturn = useMessageToasterBehavior(MESSAGE_TOASTER_DURATION);
  const [isDetailPopupShown, detailPopupMessage] = detailMessageToasterBehaviorReturn;

  // 詳細上のお気に入りボタンの振る舞い
  const [onClickBookmarkOnDetail] = useBookmarkButtonBehavior(
    useComponentInitResults, useRemoteBookmarkResults, detailMessageToasterBehaviorReturn,
  );

  const {
    onCancelSubmit,
  } = useCancellationButtonBehavior({
    dispatch: dispatchSearchResults,
    cancellationRef,
    cancelSearchRequestApi,
    setPerformanceMetrics,
  });

  /* お気に入り一覧モード用に提供される機能群 */
  useBookmarksList(
    listMode,
    bookmarksListReducerReturn,
    useComponentInitResults,
    useBookmarkRepositoryResults,
    syncBookmarkStateReturn,
  );

  // bookmarks detail functions
  const {
    onClickItem: onClickBookmarksItem,
    onClickAttachment: onClickBookmarksDetailAttachment,
    onClickAltLink: onClickBookmarksAltLink,
    onCloseDetail: onCloseBookmarksDetail,
    $bodyRef: $bookmarksDetailBodyRef,
  } = useSplitViewDetail(
    'BOOKMARKS',
    bookmarksListReducerReturn,
    useComponentInitResults,
    useSharePointApiAccessorReturn,
    useMailApiReturn,
    useChatApiReturn,
    useBookmarkRepositoryResults,
    useRemoteBookmarkResults,
    listMode,
    bookmarkDict,
    hideHighlight,
    searchMode,
  );

  /* 検索結果モード用に提供される機能群 */
  const useSearchResultListReturn = useSearchResultList(
    listModeStateReturn,
    searchRequestStateReturn,
    searchResultListReducerReturn,
    chatModeListReducerReturn,
    useComponentInitResults,
    useSharePointApiAccessorReturn,
    useUserApiReturn,
    setSearchWords,
    hideHighlight,
    useSearchRequestApiAccessorReturn,
    useSearchResultRepositoryResults,
    useGraphApiRequestQueueReturn,
    setSyncBookmark,
    cancellationRef,
    searchModeStateReturn,
    oid,
  );
  const {
    searchInputValue,
    displaySearchWord,
    onChangeSearchInput,
    onBlurSearchInput,
    onSubmitSearchInput,
    onSwitchLastSearchResult,
    replyStateReturn,
    dateFilterRef,
    sourceFilterRef,
  } = useSearchResultListReturn;

  // 一覧上の並び替えボタンの振る舞い
  const [
    onClickResultsSortButton,
    onClickBookmarksSortButton,
    onSwitchBookmarksList,
  ] = useSortButtonBehavior(
    searchMode,
    useComponentInitResults,
    isChatSearchMode
      ? chatModeListReducerReturn
      : searchResultListReducerReturn,
    bookmarksListReducerReturn,
  );

  // search result list detail functions
  const {
    onClickItem,
    onClickAttachment: onClickSearchResultAttachment,
    onClickAltLink: onClickSearchResultAltLink,
    onCloseDetail,
    $bodyRef: $searchResultDetailBodyRef,
    replyOptions,
  } = useSplitViewDetail(
    'SEARCH',
    isChatSearchMode
      ? chatModeListReducerReturn
      : searchResultListReducerReturn,
    useComponentInitResults,
    useSharePointApiAccessorReturn,
    useMailApiReturn,
    useChatApiReturn,
    useBookmarkRepositoryResults,
    useRemoteBookmarkResults,
    listMode,
    bookmarkDict,
    hideDetailHighlight,
    searchMode,
  );

  // 先頭のアイテムが変わった時走る
  // Chat モード用
  React.useEffect(() => {
    if (!isChatSearchMode) return; // 通常モードのときは何もしない

    if (activeChatModeItemId) {
      const activeItem = chatModeState.list.find(
        (item) => item.id === activeChatModeItemId,
      );
      dispatchChatMode({
        type: 'SET_ACTIVE',
        payload: {
          activeId: activeChatModeItemId,
          title: activeItem?.title ?? '',
          kind: activeItem?.kind ?? 'Other',
        },
      });
    } else {
      dispatchChatMode({
        type: 'UNSELECT',
        payload: undefined,
      });
    }
  },
  // eslint-disable-next-line react-hooks/exhaustive-deps
  [activeChatModeItemId]);

  React.useEffect(() => {
    if (activeSearchResultItemId) {
      // フィルター/ソートされた結果先頭のアイテムが変わった
      const activeItem = searchResultsState.list.find(
        (item) => item.id === activeSearchResultItemId,
      );
      dispatchSearchResults({
        type: 'SET_ACTIVE',
        payload: {
          activeId: activeSearchResultItemId,
          title: activeItem?.title ?? '',
          kind: activeItem?.kind ?? 'Other',
        },
      });
    } else {
      // フィルターされた結果一覧にアイテムがなくなった
      dispatchSearchResults({
        type: 'UNSELECT',
        payload: undefined,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeSearchResultItemId]);

  React.useEffect(() => {
    if (activeBookmarkItemId) {
      const activeItem = bookmarksState.list.find((item) => item.id === activeBookmarkItemId);
      // フィルター/ソートされた結果先頭のアイテムが変わった
      dispatchBookmarks({
        type: 'SET_ACTIVE',
        payload: {
          activeId: activeBookmarkItemId,
          title: activeItem?.title ?? '',
          kind: activeItem?.kind ?? 'Other',
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeBookmarkItemId]);

  React.useEffect(() => {
    // お気に入り一覧上でフィルターされた結果画面表示0件になった場合
    if (bookmarksState.list.length > 0 && formedBookmarksList.length === 0) {
      dispatchBookmarks({
        type: 'UNSELECT',
        payload: undefined,
      });
      dispatchBookmarks({
        type: 'SET_DATA',
        payload: {
          listMessage: SplitViewListMessage.EMPTY_BOOKMARKS,
          detailMessage: SplitViewDetailMessage.NO_BOOKMARKS,
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bookmarksState.context.filter]);

  /* 初期表示モード用に提供される機能群 */
  useInitialDisplay(
    initialDisplayReducerReturn,
    searchRequestStateReturn,
    useComponentInitResults,
    useSearchRequestApiAccessorReturn,
    useSearchResultRepositoryResults,
    useSearchResultListReturn,
    searchResultListReducerReturn,
    setIsSearchResultContextUpdated,
    searchResultsState,
    useRemoteBookmarkResults,
  );

  // detect one of the details is opened or not
  const isDetailOpenClassName = React.useMemo(() => (isDetailOpen ? 'is-open' : ''), [isDetailOpen]);

  // a class name of the detail modal on swiping back
  const [swipeBackEffectStep, setSwipeBackEffectStep] = React.useState(0);
  const swipeBackEffectClass = React.useMemo(() => `is-shifted-${swipeBackEffectStep}`, [swipeBackEffectStep]);

  // a class name of the app-info modal on swiping back
  const [swipeBackEffectInfoStep, setSwipeBackEffectInfoStep] = React.useState(0);
  const swipeBackEffectInfoClass = React.useMemo(() => `is-shifted-${swipeBackEffectInfoStep}`, [swipeBackEffectInfoStep]);
  return (
    <UserContext.Provider value={useUserApiReturn.me}>
      {/* 一覧部分 */}
      <div className="home-split-view-list">
        {/* 画面初期表示 */}
        {isInitialDisplayMode && (
          <SplitViewList
            className={swipeBackEffectClass}
            listMode={listMode}
            searchMode={searchMode}
            setSearchMode={setSearchMode}
            searchRequest={searchRequest}
            searchInputValue={searchInputValue}
            searchInputDisabled={false}
            lastSubmittedWords={displaySearchWord}
            companyName={environment.REACT_APP_SUBHEAD}
            company={environment.REACT_APP_COMPANY}
            companyRegulationSiteUrl={environment.REACT_APP_COMPANY_REGULATION_SITE_URL}
            isAISearchEnabled={environment.REACT_APP_AI_SEARCH_ENABLED}
            view={initialDisplayState.listView}
            filterOptions={initialDisplayState.context.filter}
            items={initialDisplayState.list}
            activeId={initialDisplayState.activeId}
            message={initialDisplayState.listMessage}
            state={initialDisplayState}
            appTitle={AppTitle}
            appInfoMessages={appInfoMessages}
            dispatch={dispatchInitialDisplay}
            reportEvent={reportEvent}
            onSwitchListMode={setListMode}
            onSwitchBookmarksList={onSwitchBookmarksList}
            onChangeSearchInput={onChangeSearchInput}
            onBlurSearchInput={onBlurSearchInput}
            onSubmitSearchInput={onSubmitSearchInput}
            onSwitchLastSearchResult={onSwitchLastSearchResult}
            onClickAppInfo={showAppInfo}
            onClickShowModal={showModal}
            replyStateReturn={replyStateReturn}
            dateFilterRef={dateFilterRef}
            sourceFilterRef={sourceFilterRef}
            chatMessages={chatMessages}
            onAddChatMessage={addChatMessage}
          />
        )}

        {/* お気に入り一覧 */}
        {isBookmarksListMode && (
          <SplitViewList
            className={swipeBackEffectClass}
            listMode={listMode}
            searchMode={searchMode}
            setSearchMode={setSearchMode}
            view={bookmarksState.listView}
            items={formedBookmarksList}
            activeId={activeBookmarkItemId}
            message={bookmarksState.listMessage}
            bookmarkDict={bookmarkDict}
            searchInputValue={searchInputValue}
            searchInputDisabled={false}
            lastSubmittedWords={displaySearchWord}
            companyName={environment.REACT_APP_SUBHEAD}
            company={environment.REACT_APP_COMPANY}
            companyRegulationSiteUrl={environment.REACT_APP_COMPANY_REGULATION_SITE_URL}
            isAISearchEnabled={environment.REACT_APP_AI_SEARCH_ENABLED}
            state={bookmarksState}
            sortContexts={bookmarksState.context.sort}
            filterOptions={bookmarksState.context.filter}
            emptyFilterOptions={bookmarksEmptyFilterOptions}
            appTitle={AppTitle}
            dispatch={dispatchBookmarks}
            reportEvent={reportEvent}
            onClickItem={onClickBookmarksItem}
            onClickBookmark={isTransactionPendingBookmarkRepos ? undefined : onClickBookmarkOnList}
            extendPopupTimer={extendListPopupTimer}
            onClickSort={onClickBookmarksSortButton}
            onSwitchBookmarksList={onSwitchBookmarksList}
            onSwitchListMode={setListMode}
            onChangeSearchInput={onChangeSearchInput}
            onBlurSearchInput={onBlurSearchInput}
            onSubmitSearchInput={onSubmitSearchInput}
            onClickAppInfo={showAppInfo}
            replyStateReturn={replyStateReturn}
            dateFilterRef={dateFilterRef}
            sourceFilterRef={sourceFilterRef}
            chatMessages={chatMessages}
            onAddChatMessage={addChatMessage}
          />
        )}
        {/* 検索結果一覧 AND チャットモード一覧 */}
        {isSearchListMode && (
          isChatSearchMode ? (
            <SplitViewList
              className={swipeBackEffectClass}
              listMode={listMode}
              searchMode={searchMode}
              setSearchMode={setSearchMode}
              searchRequest={searchRequest}
              view={chatModeState.listView}
              items={formedChatModeList}
              activeId={activeChatModeItemId}
              message={chatModeState.listMessage}
              bookmarkDict={bookmarkDict}
              searchInputValue={searchInputValue}
              searchInputDisabled={false}
              lastSubmittedWords={displaySearchWord}
              companyName={environment.REACT_APP_SUBHEAD}
              company={environment.REACT_APP_COMPANY}
              companyRegulationSiteUrl={environment.REACT_APP_COMPANY_REGULATION_SITE_URL}
              isAISearchEnabled={environment.REACT_APP_AI_SEARCH_ENABLED}
              sortContexts={chatModeState.context.sort}
              filterOptions={chatModeState.context.filter}
              emptyFilterOptions={chatModeEmptyFilterOptions}
              state={chatModeState}
              appTitle={AppTitle}
              appInfoMessages={appAIInfoMessages}
              dispatch={dispatchChatMode}
              reportEvent={reportEvent}
              onClickItem={onClickItem}
              onClickBookmark={isTransactionPendingBookmarkRepos
                ? undefined : onClickBookmarkOnList}
              extendPopupTimer={extendListPopupTimer}
              onClickSort={isTransactionPendingResultsRepos ? undefined : onClickResultsSortButton}
              onSwitchBookmarksList={onSwitchBookmarksList}
              onSwitchListMode={setListMode}
              onChangeSearchInput={onChangeSearchInput}
              onBlurSearchInput={onBlurSearchInput}
              onSubmitSearchInput={onSubmitSearchInput}
              onChangeItems={showListHighlight}
              onClickAppInfo={showAppInfo}
              onClickShowModal={showModal}
              onCancelSubmit={onCancelSubmit}
              replyStateReturn={replyStateReturn}
              dateFilterRef={dateFilterRef}
              sourceFilterRef={sourceFilterRef}
              chatMessages={chatMessages}
              onAddChatMessage={addChatMessage}
            />
          ) : (
            <SplitViewList
              className={swipeBackEffectClass}
              listMode={listMode}
              searchMode={searchMode}
              setSearchMode={setSearchMode}
              searchRequest={searchRequest}
              view={searchResultsState.listView}
              items={formedSearchResultsList}
              activeId={activeSearchResultItemId}
              message={searchResultsState.listMessage}
              bookmarkDict={bookmarkDict}
              searchInputValue={searchInputValue}
              searchInputDisabled={false}
              lastSubmittedWords={displaySearchWord}
              companyName={environment.REACT_APP_SUBHEAD}
              company={environment.REACT_APP_COMPANY}
              companyRegulationSiteUrl={environment.REACT_APP_COMPANY_REGULATION_SITE_URL}
              isAISearchEnabled={environment.REACT_APP_AI_SEARCH_ENABLED}
              sortContexts={searchResultsState.context.sort}
              filterOptions={searchResultsState.context.filter}
              emptyFilterOptions={searchResultEmptyFilterOptions}
              state={searchResultsState}
              appTitle={AppTitle}
              dispatch={dispatchSearchResults}
              reportEvent={reportEvent}
              onClickItem={onClickItem}
              onClickBookmark={isTransactionPendingBookmarkRepos
                ? undefined : onClickBookmarkOnList}
              extendPopupTimer={extendListPopupTimer}
              onClickSort={isTransactionPendingResultsRepos ? undefined : onClickResultsSortButton}
              onSwitchBookmarksList={onSwitchBookmarksList}
              onSwitchListMode={setListMode}
              onChangeSearchInput={onChangeSearchInput}
              onBlurSearchInput={onBlurSearchInput}
              onSubmitSearchInput={onSubmitSearchInput}
              onChangeItems={showListHighlight}
              onClickAppInfo={showAppInfo}
              onCancelSubmit={onCancelSubmit}
              replyStateReturn={replyStateReturn}
              dateFilterRef={dateFilterRef}
              sourceFilterRef={sourceFilterRef}
              chatMessages={chatMessages}
              onAddChatMessage={addChatMessage}
            />
          )
        )}

      </div>

      {/* 詳細部分 */}
      <div className={`${isDetailOpenClassName} home-split-view-detail`}>
        {/* 新着の詳細 */}
        {isInitialDisplayMode && (
          <SplitViewDetail
            className={swipeBackEffectClass}
            open={!!initialDisplayState.activeId}
            view={initialDisplayState.detailView}
            message={initialDisplayState.detailMessage}
            detail={initialDisplayState.detail}
            onSwipingBack={setSwipeBackEffectStep}
            fetchDisplayNames={fetchDisplayNames}
            groupByReactions={groupUsersByReaction}
            replyOptionsProp={replyOptions}
          />
        )}

        {/* お気に入りの詳細 */}
        {isBookmarksListMode && (
          <SplitViewDetail
            className={swipeBackEffectClass}
            open={!!bookmarksState.activeId}
            view={bookmarksState.detailView}
            message={bookmarksState.detailMessage}
            detail={bookmarksState.detail}
            isBookmarked={bookmarkDict[bookmarksState.activeId]}
            onClickAttachment={onClickBookmarksDetailAttachment}
            onClose={onCloseBookmarksDetail}
            onClickAltLink={onClickBookmarksAltLink}
            onClickBookmark={
              isTransactionPendingBookmarkRepos ? undefined : onClickBookmarkOnDetail
            }
            onSwipingBack={setSwipeBackEffectStep}
            $bodyRef={$bookmarksDetailBodyRef}
            fetchDisplayNames={fetchDisplayNames}
            groupByReactions={groupUsersByReaction}
            replyOptionsProp={replyOptions}
          />
        )}

        {/* 検索結果 AND チャットモードの詳細 */}
        {isSearchListMode && (
          isChatSearchMode ? (
            <SplitViewDetail
              className={swipeBackEffectClass}
              open={!!chatModeState.activeId}
              view={chatModeState.detailView}
              message={chatModeState.detailMessage}
              detail={chatModeState.detail}
              isBookmarked={bookmarkDict[chatModeState.activeId]}
              onClickAttachment={onClickSearchResultAttachment}
              onClose={onCloseDetail}
              onClickAltLink={onClickSearchResultAltLink}
              onClickBookmark={isTransactionPendingBookmarkRepos
                ? undefined : onClickBookmarkOnDetail}
              onSwipingBack={setSwipeBackEffectStep}
              $bodyRef={$searchResultDetailBodyRef}
              fetchDisplayNames={fetchDisplayNames}
              groupByReactions={groupUsersByReaction}
              replyOptionsProp={replyOptions}
            />
          ) : (
            <SplitViewDetail
              className={swipeBackEffectClass}
              open={!!searchResultsState.activeId}
              view={searchResultsState.detailView}
              message={searchResultsState.detailMessage}
              detail={searchResultsState.detail}
              isBookmarked={bookmarkDict[searchResultsState.activeId]}
              onClickAttachment={onClickSearchResultAttachment}
              onClose={onCloseDetail}
              onClickAltLink={onClickSearchResultAltLink}
              onChangeDetail={showDetailHighlight}
              onClickBookmark={isTransactionPendingBookmarkRepos
                ? undefined : onClickBookmarkOnDetail}
              onSwipingBack={setSwipeBackEffectStep}
              $bodyRef={$searchResultDetailBodyRef}
              fetchDisplayNames={fetchDisplayNames}
              groupByReactions={groupUsersByReaction}
              replyOptionsProp={replyOptions}
            />
          )
        )}

      </div>

      {/* お気に入り追加／削除時のポップアップ */}
      {/* 一覧側 */}
      <MessageToaster className="home-bookmark-popup-1" isActive={isListPopupShown} messageType={listPopupMessage} />
      {/* 詳細側 */}
      <MessageToaster className="home-bookmark-popup-2" isActive={isDetailPopupShown} messageType={detailPopupMessage} />

      {/* Custom Links */}
      <div className={appInfoOpen ? 'home-app-info is-open' : 'home-app-info'}>
        <div className="home-app-info-shield" />
        <AppInfoMenu
          className={`home-app-info-menu ${swipeBackEffectInfoClass}`}
          open={appInfoOpen}
          view={appInfoView}
          appInfoMessages={isChatSearchMode ? appAIInfoMessages : appInfoMessages}
          errorMessage={appInfoErrorMessage}
          onClose={hideAppInfo}
          onSwipingBack={setSwipeBackEffectInfoStep}
        />
      </div>

      {/* Teams検索対象選択モーダル */}
      <div className={modalOpen ? 'home-simple-modal is-open' : 'home-simple-modal'}>
        <div
          className="home-simple-modal-shield"
          aria-label="モーダルを閉じる"
        />
        <TeamsSettingModal
          className="home-simple-modal-content"
          open={modalOpen}
          onClose={hideModal}
          useTeamsChatsApiAccessorReturn={useTeamsChatsApiAccessorReturn}
          eventReporter={reportEvent}
        />
      </div>

    </UserContext.Provider>
  );
};

export default SplitViewContainer;
