import * as React from 'react';
import { Menu, tabListBehavior } from '@fluentui/react-northstar';

// CSS
import './TeamsSettingTabs.scss';

/**
 * タブの種類
 */
export const TeamsSettingTabType = {
  CHAT: 'チャット',
  CHANNEL: 'チャネル',
} as const;

export type TeamsSettingTabTypeValue = typeof TeamsSettingTabType[keyof typeof TeamsSettingTabType];

export interface ITeamsSettingTabsProps {
  activeTab: TeamsSettingTabTypeValue;
  onTabChange: (tab: TeamsSettingTabTypeValue) => void;
  disabled?: boolean;
  chatCount?: number;
  channelCount?: number;
}

/**
 * TeamsSettingTabs
 * チャットとチャネルを切り替えるタブコンポーネント
 */
const TeamsSettingTabs: React.FC<ITeamsSettingTabsProps> = (props) => {
  const {
    activeTab,
    onTabChange,
    disabled,
    chatCount = 0,
    channelCount = 0,
  } = props;

  // チャットタブクリックハンドラー
  const handleChatTabClick = React.useCallback(() => {
    if (disabled || activeTab === TeamsSettingTabType.CHAT) return;
    onTabChange(TeamsSettingTabType.CHAT);
  }, [disabled, activeTab, onTabChange]);

  // チャネルタブクリックハンドラー
  const handleChannelTabClick = React.useCallback(() => {
    if (disabled || activeTab === TeamsSettingTabType.CHANNEL) return;
    onTabChange(TeamsSettingTabType.CHANNEL);
  }, [disabled, activeTab, onTabChange]);

  // アクティブタブのインデックス
  const activeIndex = React.useMemo(() => (activeTab
     === TeamsSettingTabType.CHAT ? 0 : 1), [activeTab]);

  return (
    <div className={`teams-setting-tabs-container ${disabled ? 'disabled' : ''}`}>
      <Menu
        className="teams-setting-tabs-content"
        underlined
        primary
        accessibility={tabListBehavior}
        activeIndex={activeIndex}
        variables={{
          underlinedBorderColor: 'transparent',
        }}
      >
        <Menu.Item
          className="teams-setting-tabs-chat"
          index={0}
          onClick={handleChatTabClick}
          disabled={disabled}
        >
          <Menu.ItemContent>
            チャット
            {chatCount > 0 && (
              <span className="teams-setting-tabs-count">
                (
                {chatCount}
                )
              </span>
            )}
          </Menu.ItemContent>
        </Menu.Item>
        <Menu.Item
          className="teams-setting-tabs-channel"
          index={1}
          onClick={handleChannelTabClick}
          disabled={disabled}
        >
          <Menu.ItemContent>
            チャネル
            {channelCount > 0 && (
              <span className="teams-setting-tabs-count">
                (
                {channelCount}
                )
              </span>
            )}
          </Menu.ItemContent>
        </Menu.Item>
      </Menu>
    </div>
  );
};

TeamsSettingTabs.defaultProps = {
  disabled: false,
  chatCount: undefined,
  channelCount: undefined,
};

export default TeamsSettingTabs;
