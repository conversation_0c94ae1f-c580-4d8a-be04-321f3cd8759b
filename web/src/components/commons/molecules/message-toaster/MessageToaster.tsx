import * as React from 'react';
import PropTypes from 'prop-types';
import { Text, StarIcon } from '@fluentui/react-northstar';
import { mergedClassName } from '../../../../utilities/commonFunction';
import { ValueOf } from '../../../../utilities/type';
import RoundToaster from '../../atoms/round-toaster/RoundToaster';

export const ToasterMessage = {
  // デフォルト値
  BLANK: '',
  // お気に入り済
  BOOKMARKED: 'bookmarked',
  // お気に入り解除済
  UN_BOOKMARKED: 'un-bookmarked',
  // お気に入り最大件数
  MAX_BOOKMARKS: 'max-bookmarks',
  // Teams設定選択上限
  MAX_TEAMS_SELECTION: 'max-teams-selection',
  // 同じeditLinkの重複登録エラー
  DUPLICATE_EDIT_LINK: 'duplicate-edit-link',
};
export type ToasterMessageType = ValueOf<typeof ToasterMessage>;

export interface IMessageToasterProps {
  className?: string;
  // trueでポップアップを表示する
  isActive?: boolean;
  messageType?: ToasterMessageType | null;
}

// 表示メッセージ
export const ToasterPopupValue = {
  [ToasterMessage.BLANK]: '',
  [ToasterMessage.BOOKMARKED]: 'お気に入りに保存されました',
  [ToasterMessage.UN_BOOKMARKED]: 'お気に入りから削除されました',
  [ToasterMessage.MAX_BOOKMARKS]: '登録可能数の上限のためお気に入りに保存できません',
  [ToasterMessage.MAX_TEAMS_SELECTION]: '選択可能数の上限（10件）のため選択できません',
  [ToasterMessage.DUPLICATE_EDIT_LINK]: '同じ文書が既にお気に入りに登録されています',
};

const MessageToaster: React.FC<IMessageToasterProps> = ((props) => {
  const {
    className,
    isActive,
    messageType,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(
    () => mergedClassName('message-toaster', className),
    [className],
  );

  const isBookmarkStar = React.useMemo(() => {
    if (messageType === ToasterMessage.BOOKMARKED) {
      return true;
    }
    return messageType === ToasterMessage.UN_BOOKMARKED;
  }, [messageType]);

  // 表示文言
  const message = React.useMemo(() => ToasterPopupValue[messageType ?? ''], [messageType]);

  return (
    <RoundToaster className={rootClassName} isActive={isActive}>
      {isBookmarkStar && (
        <StarIcon className="message-toaster-star" outline={messageType === ToasterMessage.UN_BOOKMARKED} />
      )}
      <Text className="message-toaster-text" content={message} />
    </RoundToaster>
  );
});

MessageToaster.propTypes = {
  className: PropTypes.string,
  isActive: PropTypes.bool,
  messageType: PropTypes.string,
};

MessageToaster.defaultProps = {
  className: undefined,
  isActive: false,
  messageType: ToasterMessage.BLANK,
};

export default MessageToaster;
