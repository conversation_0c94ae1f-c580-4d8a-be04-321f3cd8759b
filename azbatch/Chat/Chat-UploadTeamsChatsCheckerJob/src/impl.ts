import "isomorphic-fetch";
import { TokenCredential } from '@azure/core-auth';
import { ClientSecretCredential } from '@azure/identity';
import { BatchRequestData, Client } from '@microsoft/microsoft-graph-client';
import { 
  createChatsMessagesRequests,
  createChatsMembersRequests 
} from '../utilities/apiRequests';
import { splitArrayIntoChunks } from '../utilities/array';
import {
  fetchGroupUsers,
  fetchJsonBatchForTeams
} from '../utilities/graph';
import { CustomLogger } from '../utilities/log';
import {
  IBatchResponseData,
  IBatchResponses,
  IModifiedUsersChatsData,
  ITeamsChatsMessages,
  ITeamsChatsMembers,
  ITeamsChatsInvalidMembers
} from '../utilities/models';
import { encrypt } from '../utilities/encryption';
import {
  updateDeleteChatsMessages,
  updateDeleteChatsMembers
} from '../utilities/cosmos';

type Logger = CustomLogger;

/******************************************************* ========== PHASE 1 - START ========== *******************************************************/
/**
 * credentialを作成
 * 必須パラメータが存在しない場合はundefinedを返却
 */
export function createCredential(
  tenantId: string,
  clientId: string,
  clientSecret: string,
): TokenCredential | undefined {

  // 必須パラメータが存在しない場合はundefined
  if (!tenantId || !clientId || !clientSecret) {
    return undefined;
  }
  return new ClientSecretCredential(tenantId, clientId, clientSecret);
}
/******************************************************* ========== PHASE 1 - END ========== *********************************************************/
/******************************************************* ========== PHASE 2 - START ========== *******************************************************/
/**
 * グループ内に所属する全ユーザーのうち、カスタムアプリを所持するユーザーだけを抽出する
 * @param logger
 * @param client
 * @param groupId
 * @param teamsAppId
 */
export async function fetchTargetUsers(
  client: Client,
  groupId: string,
  teamsAppId: string,
): Promise<IBatchResponseData[]> {

  // 対象とするユーザーを取得するためのグループID
  if (!groupId || !teamsAppId) {
    // 存在しない場合は異常終了
    return Promise.reject('NO_REQUIRED_IDS');
  }

  // グループ内のユーザー一覧を取得
  const users = await fetchGroupUsers(client, groupId);

  if (users.length === 0) {
    // 0件の場合はここで終了
    return Promise.resolve([]);
  }
  return users;
}
/******************************************************* ========== PHASE 2 - END ========== *********************************************************/
/******************************************************* ========== PHASE 4 - START ========== *******************************************************/
export async function processUsersChatsDataBatch(
  logger: Logger,
  client: Client,
  modifiedUsersChatsTableStorageData: IModifiedUsersChatsData[],
): Promise<void> {

  logger.log(`[Impl:processUsersChatsDataBatch] Total modifiedUsersChatsTableStorageData to Process: ${modifiedUsersChatsTableStorageData.length}`);
  
  const CHAT_MAX_USERS_CHATS_BATCH_COUNTS = parseInt(process.env.CHAT_MAX_USERS_CHATS_BATCH_COUNTS ?? '100');
  const splitUsersChatsBatch = splitArrayIntoChunks(modifiedUsersChatsTableStorageData, CHAT_MAX_USERS_CHATS_BATCH_COUNTS);
  logger.log(`[Impl:processUsersChatsDataBatch] Total USERS_CHATS Batches: ${splitUsersChatsBatch.length} | CHAT_MAX_USERS_CHATS_BATCH_COUNTS: ${CHAT_MAX_USERS_CHATS_BATCH_COUNTS}`);
  // logger.log(`[Impl:processUsersChatsDataBatch] splitUsersChatsBatch: ${JSON.stringify(splitUsersChatsBatch)}`); // !!!

  const totalUsersChatsBatch = splitUsersChatsBatch.length;
  for (let batchIndex = 0; batchIndex < splitUsersChatsBatch.length; batchIndex++) {
    const currentUsersChatsBatchData = splitUsersChatsBatch[batchIndex];
    const currentUsersChatsBatchNum = batchIndex + 1;

    logger.log(`\n===== 1. START BATCH | USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} with ${currentUsersChatsBatchData.length} USERS_CHATS INSIDE ===== `);
    // logger.log(`[Impl:processUsersChatsDataBatch] currentUsersChatsBatchData: ${JSON.stringify(currentUsersChatsBatchData)}`); // !!!

    try {
      // CHATS_MESSAGES
      logger.log(`
        ---------------------------------
          CHATS MESSAGES - PROCESSING
        ---------------------------------
      `);
      await processChatsMessagesBatch(logger, client, currentUsersChatsBatchData, currentUsersChatsBatchNum, totalUsersChatsBatch);

      // CHATS_MEMBERS
      logger.log(`
        ---------------------------------
          CHATS_MEMBERS - PROCESSING 
        ---------------------------------
      `);
      await processChatsMembersBatch(logger, client, currentUsersChatsBatchData, currentUsersChatsBatchNum, totalUsersChatsBatch);

      splitUsersChatsBatch[batchIndex] = [];
      if (global.gc) {
        global.gc();
      }
      await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
      logger.error(`[Impl:processUsersChatsDataBatch] Error Processing User Batch ${currentUsersChatsBatchNum}: ${error}`);
      continue;
    }

    logger.log(`===== 1. END BATCH | USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} with ${currentUsersChatsBatchData.length} USERS_CHATS INSIDE =====\n`);

  }
  splitUsersChatsBatch.length = 0;
  logger.log(`[Impl:processUsersChatsDataBatch] Completed Processing All User Batches.`);
}

async function processChatsMessagesBatch(
  logger: Logger,
  client: Client,
  currentUsersChatsBatchData: IModifiedUsersChatsData[],
  currentUsersChatsBatchNum: number,
  totalUsersChatsBatch: number
): Promise<void> {

  // logger.log(`[Impl:processChatsMessagesBatch] currentUsersChatsBatchData: ${JSON.stringify(currentUsersChatsBatchData)}`); // !!!
  logger.log(`[Impl:processChatsMessagesBatch] Total currentUsersChatsBatchData to Process: ${currentUsersChatsBatchData.length}`);
  
  const chatsMessagesBatchRequestsCreated: BatchRequestData[] = currentUsersChatsBatchData
    .filter(data => data.chatId)
    .flatMap((data) =>
      createChatsMessagesRequests(data?.chatId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url    
      }))
    );

  logger.log(`[Impl:processChatsMessagesBatch] Created: ${chatsMessagesBatchRequestsCreated.length} CHATS_MESSAGES Requests`);
 
  const CHAT_MAX_GRAPH_API_CHATS_MESSAGES_BATCH_COUNTS = parseInt(process.env.CHAT_MAX_GRAPH_API_CHATS_MESSAGES_BATCH_COUNTS ?? '50');
  const splitChatsMessagesBatchRequests = splitArrayIntoChunks(chatsMessagesBatchRequestsCreated, CHAT_MAX_GRAPH_API_CHATS_MESSAGES_BATCH_COUNTS);
  logger.log(`[Impl:processChatsMessagesBatch] Total Split CHATS_MESSAGES Batch: ${splitChatsMessagesBatchRequests.length} | CHAT_MAX_GRAPH_API_CHATS_MESSAGES_BATCH_COUNTS: ${CHAT_MAX_GRAPH_API_CHATS_MESSAGES_BATCH_COUNTS}`);
  // logger.log(`[Impl:processChatsMessagesBatch] splitChatsMessagesBatchRequests: ${JSON.stringify(splitChatsMessagesBatchRequests)}`); // !!!

  const totalChatsMessagesBatchRequests = splitChatsMessagesBatchRequests.length;
  for (let i = 0; i < totalChatsMessagesBatchRequests; i++) {
    const currentChatsMessagesBatchRequestsNum = i + 1;

    logger.log(`\n----- 2. START BATCH | API REQUEST - CHATS_MESSAGES: ${currentChatsMessagesBatchRequestsNum} of ${totalChatsMessagesBatchRequests} / USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} -----`);
    
    try {
      const currentSplitChatsMessagesBatchRequests = splitChatsMessagesBatchRequests[i];
      // logger.log(`[Impl:processChatsMessagesBatch] currentSplitChatsMessagesBatchRequests == ${JSON.stringify(currentSplitChatsMessagesBatchRequests)}`); // !!!

      const batchResultChatsMessages = await fetchJsonBatchForTeams(logger, client, currentSplitChatsMessagesBatchRequests);
      // // *** View Raw Response
      // logger.log(`\n\n*** [Impl:processChatsMessagesBatch] batchResultChatsMessages == ${JSON.stringify(batchResultChatsMessages)}`); // !!!

      const totalBatchResultChatsCount = (batchResultChatsMessages.responses ?? []).reduce((total, response) => {
        return total + (response.body?.value?.length ?? 0);
      }, 0);
      logger.log(`[Impl:processSingleResultChatsMessages] Total totalBatchResultChatsCount: ${totalBatchResultChatsCount}`);

      if (!batchResultChatsMessages.responses || batchResultChatsMessages.responses.length === 0) {
        logger.log(`[Impl:processChatsMessagesBatch] No Responses = 📡 API REQUEST - CHATS_MESSAGES`);
        continue;
      }

      const filteredBatchResults = {
        ...batchResultChatsMessages,
        responses: batchResultChatsMessages.responses.map(response => ({
          ...response,
          body: {
            ...response.body,
            value: (response.body?.value ?? []).filter((message: any) => {
              // Filter out system messages
              const isValidMessageType = message.messageType !== 'systemEventMessage' && 
                                      message.messageType !== 'unknownFutureValue';
              
              // Keep only messages that are either deleted OR edited
              const isDeleted = message.deletedDateTime !== null && message.deletedDateTime !== undefined;
              const isEdited = message.lastEditedDateTime !== null && message.lastEditedDateTime !== undefined;
              
              // Must be valid message type AND (deleted OR edited)
              return isValidMessageType && (isDeleted || isEdited);
            }),
            "@odata.count": (response.body?.value ?? []).filter((message: any) => {
              const isValidMessageType = message.messageType !== 'systemEventMessage' && 
                                      message.messageType !== 'unknownFutureValue';
              const isDeleted = message.deletedDateTime !== null && message.deletedDateTime !== undefined;
              const isEdited = message.lastEditedDateTime !== null && message.lastEditedDateTime !== undefined;
              
              return isValidMessageType && (isDeleted || isEdited);
            }).length
          }
        }))
      };
      // logger.log(`\n\n[Impl:processSingleResultChatsMessages] filteredMessages: ${JSON.stringify(filteredBatchResults)}`);  // !!!

      const totalFilteredCount = filteredBatchResults.responses.reduce((total, response) => {
        return total + (response.body?.value?.length ?? 0);
      }, 0);
      logger.log(`[Impl:processSingleResultChatsMessages] Total filteredMessages: ${totalFilteredCount} w/o systemEventMessage and unknownFutureValue, only deleted/edited`);

      await processBatchResultChatsMessages(logger, filteredBatchResults, currentChatsMessagesBatchRequestsNum, totalChatsMessagesBatchRequests, currentUsersChatsBatchNum, totalUsersChatsBatch);

      batchResultChatsMessages.responses = [];
      splitChatsMessagesBatchRequests[i] = [];
      await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
      logger.error(`[Impl:processChatsMessagesBatch] Error Processing = 📡 API REQUEST - CHATS_MESSAGES ${i + 1}: ${error}`);
      continue;
    }

    logger.log(`----- 2. END BATCH | API REQUEST - CHATS_MESSAGES: ${currentChatsMessagesBatchRequestsNum} of ${totalChatsMessagesBatchRequests} / USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} -----`);
  }
  chatsMessagesBatchRequestsCreated.length = 0;
  splitChatsMessagesBatchRequests.length = 0;
}

async function processBatchResultChatsMessages(
  logger: Logger,
  batchResultChatsMessages: IBatchResponses,
  currentChatsMessagesBatchRequestsNum: number,
  totalChatsMessagesBatchRequests: number,
  currentUsersChatsBatchNum: number,
  totalUsersChatsBatch: number
): Promise<void> {

  const totalBatchResultChatsMessages = batchResultChatsMessages.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultChatsMessages; j++) {
    const currentBatchResultChatsMessagesNum = j + 1;
    const batchResultChatsMessagesResponses = (batchResultChatsMessages.responses ?? [])[j];
    const chatId = batchResultChatsMessagesResponses.id;

    logger.log(`\n\n***** 3. START BATCH | PROCESSSING RESULT - CHATS_MESSAGES: chatId: ${chatId} / API RESULT - CHATS_MESSAGES: ${currentBatchResultChatsMessagesNum} of ${totalBatchResultChatsMessages} / API REQUEST - CHATS_MESSAGES: ${currentChatsMessagesBatchRequestsNum} of ${totalChatsMessagesBatchRequests} / USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} *****`);

    if (!batchResultChatsMessagesResponses || batchResultChatsMessagesResponses.status !== 200) {
      logger.log(`[Impl:processBatchResultChatsMessages] Skipping chatId: ${chatId} with Error Data: ${batchResultChatsMessagesResponses?.status}`);
      continue;
    }

    const singleResultChatsMessages = { responses: [batchResultChatsMessagesResponses] };
    // logger.info(`[Impl:processBatchResultChatsMessages] singleResultChatsMessages == ${JSON.stringify(singleResultChatsMessages)}`);  // !!!
                                                                      
    const modifiedChatsMessagesChunk = processSingleResultChatsMessages([singleResultChatsMessages], logger);
    // // *** View Raw Response
    // logger.info(`\n\n[Impl:processBatchResultChatsMessages] modifiedChatsMessagesChunk == ${JSON.stringify(modifiedChatsMessagesChunk)}`); // !!!

    // Updating Deleting Start...
    const totalModifiedChatsMessageChunk = modifiedChatsMessagesChunk.length;
    for (let k = 0; k < totalModifiedChatsMessageChunk; k++) {
      const messagesChunk = modifiedChatsMessagesChunk[k];
      const currentModifiedChatsMessagesNum = k + 1;
      if (!messagesChunk) {
        logger.info(`[Impl:processBatchResultChatsMessages] Skipping Undefined CHATS_MESSAGES at Index: ${k}`);
        continue;
      }
      logger.log(`+++++ 4. START BATCH | Updating Deleting CHATS_MESSAGES IN COSMOS_DB: Chunk: ${currentModifiedChatsMessagesNum} of ${totalModifiedChatsMessageChunk} with ${messagesChunk.body?.value?.length} CHATS_MESSAGES Inside +++++`);
      logger.log(`+++++ PROCESSSING RESULT - CHATS_MESSAGES: chatId: ${chatId} / API RESULT - CHATS_MESSAGES: ${currentBatchResultChatsMessagesNum} of ${totalBatchResultChatsMessages} / API REQUEST - CHATS_MESSAGES: ${currentChatsMessagesBatchRequestsNum} of ${totalChatsMessagesBatchRequests} / USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} +++++`);
      try {
        logger.info(`[Impl:processBatchResultChatsMessages] Updating Deleting CHATS_MESSAGES...`);
        await updateDeleteChatsMessages(logger, [messagesChunk]);
        logger.info(`[Impl:processBatchResultChatsMessages] Successfully Updating Deleting CHATS_MESSAGES...`);

      } catch (error) {
        logger.error(`[Impl:processBatchResultChatsMessages] Failed Updating Deleting: ${error}`);
        continue;
      }
      modifiedChatsMessagesChunk[k] = {} as IBatchResponseData;
      logger.log(`+++++ PROCESSSING RESULT - CHATS_MESSAGES: chatId: ${chatId} / API RESULT - CHATS_MESSAGES: ${currentBatchResultChatsMessagesNum} of ${totalBatchResultChatsMessages} / API REQUEST - CHATS_MESSAGES: ${currentChatsMessagesBatchRequestsNum} of ${totalChatsMessagesBatchRequests} / USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} +++++`);
      logger.log(`+++++ 4. END BATCH | Updating Deleting CHATS_MESSAGES IN COSMOS_DB: Chunk: ${currentModifiedChatsMessagesNum} of ${totalModifiedChatsMessageChunk} with ${messagesChunk.body?.value?.length} CHATS_MESSAGES Inside +++++`);
    }
    // Updating Deleting End...

    modifiedChatsMessagesChunk.length = 0;
    if (global.gc) {
      global.gc();
    }
    await new Promise(resolve => setTimeout(resolve, 3000));
    logger.log(`***** 3. END BATCH | PROCESSSING RESULT - CHATS_MESSAGES: chatId: ${chatId} / API RESULT - CHATS_MESSAGES: ${currentBatchResultChatsMessagesNum} of ${totalBatchResultChatsMessages} / API REQUEST - CHATS_MESSAGES: ${currentChatsMessagesBatchRequestsNum} of ${totalChatsMessagesBatchRequests} / USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} *****`);
  }
}

const CHAT_MAX_CHATS_MESSAGES_CHUNK_SIZE = parseInt(process.env.CHAT_MAX_CHATS_MESSAGES_CHUNK_SIZE ?? '50');
function processSingleResultChatsMessages(
  singleResultChatsMessages: IBatchResponses[], 
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResultChatsMessages) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultChatsMessages] Skipping Invalid CHATS_MESSAGES Result == ${JSON.stringify(result)}`);
      continue;
    }

    for (const response of result.responses) {
      const totalChatsMessages = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultChatsMessages] Total CHATS_MESSAGES in response.body.value: ${totalChatsMessages}`);

      const chatId = response.id ?? '';
      const allTeamsChatMessages = response.body?.value as ITeamsChatsMessages[];
      const transformedMessages = allTeamsChatMessages.map((item) => {
        const hasAttachments = 
          'attachments' in item && 
          Array.isArray((item as any).attachments) && 
          (item as any).attachments.length > 0 &&
          (item as any).attachments.some((attachment: any) => 
            attachment.contentType === "reference"
          ) &&
          !(item as any).attachments.every((attachment: any) => 
            attachment.contentType === "messageReference"
          );
        
        return {
          ...item,
          hasAttachments
        };
      });
      //logger.log(`[Impl:processSingleResultChatsMessages] transformedMessages: ${JSON.stringify(transformedMessages)}`); // !!!

      // Process messages in chunks
      const chunkSize = CHAT_MAX_CHATS_MESSAGES_CHUNK_SIZE;
      for (let i = 0; i < transformedMessages.length; i += chunkSize) {
        const chatsMessagesAfterChunk = transformedMessages.slice(i, i + chunkSize);
        // logger.info(`\n\n[Impl:processSingleResultChatsMessages] chatsMessagesAfterChunk: ${JSON.stringify(chatsMessagesAfterChunk)}`); // !!!

        processChatsMessagesChunk(chatId, chatsMessagesAfterChunk, allProcessedData, logger);
        chatsMessagesAfterChunk.length = 0;
      }
      allTeamsChatMessages.length = 0;
    }
  }
  // logger.info(`\n\n[Impl:processSingleResultChatsMessages] allProcessedData == ${JSON.stringify(allProcessedData)}`); // !!!
  return allProcessedData;
}

function processChatsMessagesChunk(
  chatId: string,
  chatsMessagesAfterChunk: ITeamsChatsMessages[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: ITeamsChatsMessages[] = [];
  let skippedCount = 0;

  for (const item of chatsMessagesAfterChunk) {
    if (hasEmptyRequiredFields(item)) {
      skippedCount++;
      continue;
    }
    processedValues.push(createEncryptedMessage(item));
  }

  if (skippedCount > 0) {
    logger.log(`[Impl:hasEmptyRequiredFields] Skipped ${skippedCount} CHATS_MESSAGES with Null Values in this Chunk`);
  }

  if (processedValues.length > 0) {
    allProcessedData.push({
      id: chatId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processChatsMessagesChunk] Successfully Modified: ${processedValues.length} CHATS_MESSAGES Inside Chunk | CHAT_MAX_CHATS_MESSAGES_CHUNK_SIZE: ${CHAT_MAX_CHATS_MESSAGES_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFields(item: ITeamsChatsMessages): boolean {
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  return isEmpty(item.id)
}

function createEncryptedMessage(item: ITeamsChatsMessages): ITeamsChatsMessages {
  return {
    security_user_id: [],
    id: item.id,
    kind: "Chat",
    replyToId: item.replyToId ? encrypt(item.replyToId) : null,
    messageType: item.messageType ? encrypt(item.messageType) : null,
    createdDateTime: item.createdDateTime ?? null,
    lastModifiedDateTime: item.lastModifiedDateTime ?? null,
    lastEditedDateTime: item.lastEditedDateTime ?? null,
    deletedDateTime: item.deletedDateTime ?? null,
    subject: item.subject ? encrypt(item.subject) : null,
    chatId: item.chatId ?? null,
    from: item.from ? {
      application: item.from.application ? {
        displayName: encrypt(item.from.application.displayName)
      } : null,
      device: item.from.device ? {
        displayName: encrypt(item.from.device.displayName)
      } : null,
      user: item.from.user ? {
        id: encrypt(item.from.user.id),
        displayName: encrypt(item.from.user.displayName)
      } : null,
    } : null,
    body: item.body ? {
      content: encrypt(item.body.content)
    } : null,
    hasAttachments: item.hasAttachments,
    channelIdentity: item.channelIdentity ? {
      teamId: item.channelIdentity.teamId,
      channelId: encrypt(item.channelIdentity.channelId)
    } : null,
    softDelete: false,
  };
}

// function createEncryptedMessage(item: ITeamsChatsMessages): ITeamsChatsMessages {
//   return {
//     security_user_id: [],
//     id: item.id,
//     kind: "Chat",
//     replyToId: item.replyToId ? item.replyToId : null,
//     messageType: item.messageType ? item.messageType : null,
//     createdDateTime: item.createdDateTime ?? null,
//     lastModifiedDateTime: item.lastModifiedDateTime ?? null,
//     lastEditedDateTime: item.lastEditedDateTime ?? null,
//     deletedDateTime: item.deletedDateTime ?? null,
//     subject: item.subject ? item.subject : null,
//     chatId: item.chatId ?? null,
//     from: item.from ? {
//       application: item.from.application ? {
//         displayName: item.from.application.displayName
//       } : null,
//       device: item.from.device ? {
//         displayName: item.from.device.displayName
//       } : null,
//       user: item.from.user ? {
//         id: item.from.user.id,
//         displayName: item.from.user.displayName
//       } : null,
//     } : null,
//     body: item.body ? {
//       content: item.body.content
//     } : null,
//     hasAttachments: item.hasAttachments,
//     channelIdentity: item.channelIdentity ? {
//       teamId: item.channelIdentity.teamId,
//       channelId: item.channelIdentity.channelId
//     } : null,
//     softDelete: false,
//   };
// }

/******************************************************* ========== PHASE 4 - END ========== *********************************************************/
/******************************************************* ========== PHASE 5 - START ========== *******************************************************/
export async function processChatsMembersBatch(
  logger: Logger,
  client: Client,
  currentUsersChatsBatchData: IModifiedUsersChatsData[],
  currentUsersChatsBatchNum: number,
  totalUsersChatsBatch: number,
): Promise<void> {

  // logger.log(`[Impl:processChatsMembersBatch] currentUsersChatsBatchData: ${JSON.stringify(currentUsersChatsBatchData)}`); // !!!
  logger.log(`[Impl:processChatsMembersBatch] Total currentUsersChatsBatchData to Process: ${currentUsersChatsBatchData.length}`);
  
  const chatsMembersBatchRequestsCreated: BatchRequestData[] = currentUsersChatsBatchData
    .filter(data => data.chatId)
    .flatMap((data) =>
      createChatsMembersRequests(data?.chatId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processChatsMembersBatch] Created: ${chatsMembersBatchRequestsCreated.length} CHATS_MEMBERS Requests`);
 
  const CHAT_MAX_GRAPH_API_CHATS_MEMBERS_BATCH_COUNTS = parseInt(process.env.CHAT_MAX_GRAPH_API_CHATS_MEMBERS_BATCH_COUNTS ?? '20');
  const splitChatsMembersBatchRequests = splitArrayIntoChunks(chatsMembersBatchRequestsCreated, CHAT_MAX_GRAPH_API_CHATS_MEMBERS_BATCH_COUNTS);
  logger.log(`[Impl:processChatsMembersBatch] Total Split CHATS_MEMBERS Batch: ${splitChatsMembersBatchRequests.length} | CHAT_MAX_GRAPH_API_CHATS_MEMBERS_BATCH_COUNTS: ${CHAT_MAX_GRAPH_API_CHATS_MEMBERS_BATCH_COUNTS}`);
  // logger.log(`[Impl:processChatsMembersBatch] splitChatsMembersBatchRequests: ${JSON.stringify(splitChatsMembersBatchRequests)}`); // !!!

  const totalChatsMembersBatchRequests = splitChatsMembersBatchRequests.length;
  for (let i = 0; i < totalChatsMembersBatchRequests; i++) {
    const currentChatsMembersBatchRequestsNum = i + 1;

    logger.log(`\n----- 2. START BATCH | API REQUEST - CHATS_MEMBERS: ${currentChatsMembersBatchRequestsNum} of ${totalChatsMembersBatchRequests} / USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} -----`);
    
    try {
      const currentSplitChatsMembersBatchRequests = splitChatsMembersBatchRequests[i];
      // logger.log(`[Impl:processChatsMembersBatch] currentSplitChatsMembersBatchRequests == ${JSON.stringify(currentSplitChatsMembersBatchRequests)}`); // !!!

      const batchResultChatsMembers = await fetchJsonBatchForTeams(logger, client, currentSplitChatsMembersBatchRequests);
      // // *** View Raw Response
      // logger.log(`\n\n*** [Impl:processChatsMembersBatch] batchResultChatsMembers == ${JSON.stringify(batchResultChatsMembers)}`); // !!!

      if (!batchResultChatsMembers.responses || batchResultChatsMembers.responses.length === 0) {
        logger.log(`[Impl:processChatsMembersBatch] No Responses in API REQUEST - CHATS_MEMBERS`);
        continue;
      }

      // Compare the users
      const chatUsersLookup = new Map<string, Set<string>>();
      const chatUsersStatusLookup = new Map<string, Map<string, string>>();
      for (const chat of currentUsersChatsBatchData) {
        chatUsersLookup.set(chat.chatId, new Set(chat.users.map(user => user.id)));
        const userStatusMap = new Map<string, string>();
        chat.users.forEach(user => {
          userStatusMap.set(user.id, user.status);
        });
        chatUsersStatusLookup.set(chat.chatId, userStatusMap);
      }

      const filteredBatchResultChatsMembers = {
        responses: (batchResultChatsMembers.responses ?? []).map(response => {
          if (!response.body?.value || !Array.isArray(response.body.value)) {
            return response;
          }
          const chatId = response.id ?? '';
          const expectedUsersForChat = chatUsersLookup.get(chatId);
          const userStatusMap = chatUsersStatusLookup.get(chatId);

          if (!expectedUsersForChat || !userStatusMap) {
            return {
              ...response,
              body: {
                ...response.body,
                value: [],
                invalidMembers: []
              }
            };
          }
          
          const actualUserIds = new Set(
            response.body.value
              .map(member => (member as { userId: string }).userId)
              .filter(userId => userId)
          );
          const inCurrentButNotInBatchResult = Array.from(expectedUsersForChat)
            .filter(expectedUserId => !actualUserIds.has(expectedUserId))
            .map(missingUserId => ({ id: missingUserId }));
          const inBatchResultButNotInCurrent = Array.from(actualUserIds)
            .filter(actualUserId => !expectedUsersForChat.has(actualUserId))
            .map(unexpectedUserId => ({ id: unexpectedUserId }));
          const deletedUsers = Array.from(actualUserIds)
            .filter(actualUserId => {
              return expectedUsersForChat.has(actualUserId) && 
                    userStatusMap.get(actualUserId) === "deleted";
            })
            .map(deletedUserId => ({ id: deletedUserId }));

          const invalidMembers = [...inCurrentButNotInBatchResult, ...inBatchResultButNotInCurrent, ...deletedUsers];

          const seenUserIds = new Set<string>();
          const validMembers = response.body.value.filter(member => {
            const userId = (member as { userId: string }).userId;
            if (!userId) return false;
            if (!expectedUsersForChat.has(userId)) return false;
            if (seenUserIds.has(userId)) return false;
            seenUserIds.add(userId);
            return true;
          });

          return {
            ...response,
            body: {
              ...response.body,
              value: validMembers,
              invalidMembers: invalidMembers
            }
          };
        })
      };
      // logger.log(`\n[Impl:processChatsMembersBatch] filteredBatchResultChatsMembers == ${JSON.stringify(filteredBatchResultChatsMembers)}`);

      await processBatchResultChatsMembers(logger, filteredBatchResultChatsMembers, currentChatsMembersBatchRequestsNum, totalChatsMembersBatchRequests, currentUsersChatsBatchNum, totalUsersChatsBatch);

      batchResultChatsMembers.responses = [];
      splitChatsMembersBatchRequests[i] = [];
      await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
      logger.error(`[Impl:processChatsMembersBatch] Error Processing API REQUEST - CHATS_MEMBERS: ${i + 1}: ${error}`);
      continue;
    }
   
    logger.log(`----- 2. END BATCH | API REQUEST - CHATS_MEMBERS: ${currentChatsMembersBatchRequestsNum} of ${totalChatsMembersBatchRequests} / USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch}-----`);

  }
  chatsMembersBatchRequestsCreated.length = 0;
  splitChatsMembersBatchRequests.length = 0;
}

async function processBatchResultChatsMembers(
  logger: Logger,
  filteredBatchResultChatsMembers: IBatchResponses,
  currentChatsMembersBatchRequestsNum: number,
  totalChatsMembersBatchRequests: number,
  currentUsersChatsBatchNum: number,
  totalUsersChatsBatch: number
): Promise<void> {

  const totalBatchResultChatsMembers = filteredBatchResultChatsMembers.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultChatsMembers; j++) {
    const currentBatchResultChatsMembersNum = j + 1;
    const batchResultChatsMembersResponses = (filteredBatchResultChatsMembers.responses ?? [])[j];
    const chatId = batchResultChatsMembersResponses.id;

    logger.log(`\n\n***** 3. START BATCH | PROCESSSING RESULT - CHATS_MEMBERS: chatId: ${chatId} / API RESULT - CHATS_MEMBERS: ${currentBatchResultChatsMembersNum} of ${totalBatchResultChatsMembers} / API REQUEST - CHATS_MEMBERS: ${currentChatsMembersBatchRequestsNum} of ${totalChatsMembersBatchRequests} / USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} *****`);

    if (!batchResultChatsMembersResponses || batchResultChatsMembersResponses.status !== 200) {
      logger.log(`[Impl:processBatchResultChatsMembers] Skipping chatId: ${chatId} with Error Data: ${batchResultChatsMembersResponses?.status}`);
      continue;
    }

    const singleResultChatsMembers = { responses: [batchResultChatsMembersResponses] };
    // logger.info(`[Impl:processBatchResultChatsMembers] singleResultChatsMembers == ${JSON.stringify(singleResultChatsMembers)}`);  // !!!

    const modifiedChatsMembersChunk = processSingleResultChatsMembers([singleResultChatsMembers], logger);
    // // *** View Raw Response
    // logger.info(`\n\n[Impl:processBatchResultChatsMembers] modifiedChatsMembersChunk == ${JSON.stringify(modifiedChatsMembersChunk)}`);  // !!!

    const isEmpty = !modifiedChatsMembersChunk || modifiedChatsMembersChunk.length === 0
    if (isEmpty) {
      logger.info(`[Impl:processBatchResultChatsMembers] modifiedChatsMembersChunk is EMPTY`);
    }

    // Updating Start...
    const totalModifiedChatsMembersChunk = modifiedChatsMembersChunk.length;
    for (let k = 0; k < totalModifiedChatsMembersChunk; k++) {
      const membersChunk = modifiedChatsMembersChunk[k];
      const currentModifiedMembersChunkNum = k + 1;
      if (!membersChunk) {
        logger.info(`[Impl:processBatchResultChatsMembers] Skipping Undefined CHATS_MESSAGES at Index: ${k}`);
        continue;
      }

      logger.log(`+++++ 4. START BATCH | UPDATING CHATS_MEMBERS IN COSMOS_DB: Chunk: ${currentModifiedMembersChunkNum} of ${totalModifiedChatsMembersChunk} with ${membersChunk.body?.value?.length} CHATS_MEMBERS Inside +++++`);
      logger.log(`+++++ PROCESSSING RESULT - CHATS_MEMBERS: chatId: ${chatId} / API RESULT - CHATS_MEMBERS: ${currentBatchResultChatsMembersNum} of ${totalBatchResultChatsMembers} / API REQUEST - CHATS_MEMBERS: ${currentChatsMembersBatchRequestsNum} of ${totalChatsMembersBatchRequests} / USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} +++++`);
      try {
        logger.info(`[Impl:processBatchResultChatsMembers] Updating CHATS_MEMBERS...`);
        await updateDeleteChatsMembers(logger, [membersChunk]);
        logger.info(`[Impl:processBatchResultChatsMembers] Successfully Updated CHATS_MEMBERS...`);

      } catch (error) {
        logger.error(`[Impl:processBatchResultChatsMembers] Failed Updating: ${error}`);
        continue;
      }
      modifiedChatsMembersChunk[k] = {} as IBatchResponseData;
      logger.log(`+++++ PROCESSSING RESULT - CHATS_MEMBERS: chatId: ${chatId} / API RESULT - CHATS_MEMBERS: ${currentBatchResultChatsMembersNum} of ${totalBatchResultChatsMembers} / API REQUEST - CHATS_MEMBERS: ${currentChatsMembersBatchRequestsNum} of ${totalChatsMembersBatchRequests} / USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} +++++`);
      logger.log(`+++++ 4. END BATCH | UPDATING CHATS_MEMBERS IN COSMOS_DB: Chunk: ${currentModifiedMembersChunkNum} of ${totalModifiedChatsMembersChunk} with ${membersChunk.body?.value?.length} CHATS_MEMBERS Inside +++++`);
    }
    // Updating End...

    modifiedChatsMembersChunk.length = 0;
    if (global.gc) {
      global.gc();
    }
    await new Promise(resolve => setTimeout(resolve, 3000));

    logger.log(`***** 3. END BATCH | PROCESSSING RESULT - CHATS_MEMBERS: chatId: ${chatId} / API RESULT - CHATS_MEMBERS: ${currentBatchResultChatsMembersNum} of ${totalBatchResultChatsMembers} / API REQUEST - CHATS_MEMBERS: ${currentChatsMembersBatchRequestsNum} of ${totalChatsMembersBatchRequests} / USERS_CHATS BATCH: ${currentUsersChatsBatchNum} of ${totalUsersChatsBatch} *****`);
  }
}

const CHAT_MAX_CHATS_MEMBERS_CHUNK_SIZE = parseInt(process.env.CHAT_MAX_CHATS_MEMBERS_CHUNK_SIZE ?? '50');
function processSingleResultChatsMembers(
  singleResultChatsMembers: IBatchResponses[], 
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResultChatsMembers) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultChatsMembers] Skipping Invalid CHATS_MEMBERS Result == ${JSON.stringify(result)}`);
      continue;
    }

    for (const response of result.responses) {
      const totalChatsMembers = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultChatsMembers] Total CHATS_MEMBERS in response.body.value: ${totalChatsMembers}`);

      const teamsId = response.id ?? '';
      const allChatsMembers = response.body?.value as ITeamsChatsMembers[];
      const allChatsInvalidMembers = response.body?.invalidMembers as ITeamsChatsInvalidMembers[];
      // logger.log(`\n\n[Impl:processSingleResultChatsMembers] allChatsMembers: ${JSON.stringify(allChatsMembers)}`); // !!!

      // Process members in chunks
      const chunkSize = CHAT_MAX_CHATS_MEMBERS_CHUNK_SIZE;
      for (let i = 0; i < allChatsInvalidMembers.length; i += chunkSize) {
        const chatInvalidMembersAfterChunk = allChatsInvalidMembers.slice(i, i + chunkSize);
        // logger.info(`\n\n[Impl:processSingleResultChatsMembers] chatInvalidMembersAfterChunk: ${JSON.stringify(chatInvalidMembersAfterChunk)}`); // !!!

        processChatsMembersBatchChunk(teamsId, chatInvalidMembersAfterChunk, allProcessedData, logger);
        chatInvalidMembersAfterChunk.length = 0;
      }
      allChatsMembers.length = 0;
    }
  }
  // logger.info(`\n\n[Impl:processSingleResultChatsMembers] allProcessedData == ${JSON.stringify(allProcessedData)}`); // !!!
  return allProcessedData;
}

function processChatsMembersBatchChunk(
  teamsId: string,
  chatInvalidMembersAfterChunk: ITeamsChatsInvalidMembers[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: ITeamsChatsInvalidMembers[] = [];
  let skippedCount = 0;

  for (const item of chatInvalidMembersAfterChunk) {
  if (hasEmptyRequiredFieldsMembers(item)) {
      skippedCount++;
      continue;
    }
    processedValues.push(createFieldMembers(item));
  }

  if (skippedCount > 0) {
    logger.log(`[Impl:hasEmptyRequiredFields] Skipped ${skippedCount} CHATS_MEMBERS with null values in this chunk`);
  }

  if (processedValues.length > 0) {
    allProcessedData.push({
      id: teamsId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processChatsMembersBatchChunk] Successfully Modified CHATS_MEMBERS: ${processedValues.length} CHATS_MEMBERS Inside Chunk | CHAT_MAX_CHATS_MEMBERS_CHUNK_SIZE: ${CHAT_MAX_CHATS_MEMBERS_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFieldsMembers(item: ITeamsChatsInvalidMembers): boolean {
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  return isEmpty(item.id)
}

function createFieldMembers(item: ITeamsChatsInvalidMembers): ITeamsChatsInvalidMembers {
  return {
    id: item.id
  };
}
/******************************************************* ========== PHASE 5 - END ========== *********************************************************/