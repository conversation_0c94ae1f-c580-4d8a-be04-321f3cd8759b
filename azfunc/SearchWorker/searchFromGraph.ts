import { BatchRequestData, Client } from "@microsoft/microsoft-graph-client";
import Dictionary from "../types/Dictionary";
import HttpStatusCode from "../types/HttpStatusCode";
import IBatchResult from "../types/IBatchResult";
import ISearchContext from "../types/ISearchContext";
import ISearchParameters, { ISearchConditionTree } from "../types/ISearchParameters"
import { executeAll } from "../utilities/api";
import { toUTC } from "../utilities/date";
import { createGraphClientAttane, ISearchQuery } from "../utilities/graph";
import { MAX_PAGE_GRAPH, MAX_RESULT_GRAPH } from "../utilities/lib";

export interface ISearchFromGraphSourceParameters {
  entityType: string,
  fields: string[],
  getIds: (r: any[]) => string[],
}

interface IGraphSearchResult {
  searchTerms: string[],
  hitsContainers: {
    hits: any[],
  }[],
}

/**
 * メールを検索します。
 * @param parameters 検索パラメータ
 * @returns 取得した項目情報の配列
 * @description
 * 項目の取得は、GET /me/messages/[0]
 */
export function searchFromGraphMail(parameters: ISearchParameters): Promise<IBatchResult[]> {
  return searchFromGraph({
    // 「メールデータを検索してください」というリクエスト
    entityType: 'message',
    // 追加で指定するフィールドがないことを示しています。
    fields: [],
     // 検索結果から「メールのIDリスト」を抽出するロジックを提供。
    getIds: (r: any) => {
      parameters.logger?.verbose(`response: ${r.value}`);
      const p = (r.value as IGraphSearchResult[]).map(h => h.hitsContainers.map(i => i.hits ? i.hits.filter(v => v.hitId).map(v => v.hitId as string) : undefined).filter(v => v) as string[][]);
      return p.flat(2) ?? [];
    },
  }, parameters);
}

/**
 * グループチャットを検索します。
 * @param parameters 検索パラメータ
 * @returns 取得した項目情報の配列
 * @description
 * 項目の取得は、項目を|で分割して、GET /me/chats/[2]/messages/[3]
 */
export function searchFromGraphChat(parameters: ISearchParameters): Promise<IBatchResult[]> {
  return searchFromGraph({
    entityType: 'chatMessage',
    fields: [],
    getIds: (r: any) => {
      const p = (r.value as IGraphSearchResult[]).map(h => h.hitsContainers.map(i => i.hits ? i.hits.map(v => {
        if (v.resource?.channelIdentity?.teamId && v.resource?.channelIdentity?.channelId && v.resource?.id) {
          // Team Channel
          return `team|${v.resource.channelIdentity.teamId}|${v.resource.channelIdentity.channelId}|${v.resource.id}`;
        }
        if (v.resource?.chatId && v.resource?.id) {
          // Chat
          return `chat||${v.resource.chatId}|${v.resource.id}`;
        }
        return '';
      }).filter(v => v) : undefined).filter(v => v) as string[][]);
      return p.flat(2) ?? [];
    },
  }, parameters);
}

/**
 * ファイルを検索します。
 * @param parameters 検索パラメータ
 * @returns 取得した項目情報の配列
 * @description
 * 項目の取得は、項目を|で分割して、GET /sites/[0]/drive/items/[1]
 */
export function searchFromGraphFile(parameters: ISearchParameters): Promise<IBatchResult[]> {
  return searchFromGraph({
    entityType: 'driveItem',
    fields: [],
    getIds: (r: any) => {
      const p = (r.value as IGraphSearchResult[]).map(h => h.hitsContainers.map(i => i.hits ? i.hits.map(
        v => v.resource?.id && v.resource?.parentReference?.siteId ? `${v.resource.parentReference.siteId}|${v.resource.id}` : ''
      ).filter(v => v) : undefined).filter(v => v) as string[][]);
      return p.flat(2) ?? [];
    },
  }, parameters);
}

export function searchFromGraphSPO(parameters: ISearchParameters): Promise<IBatchResult[]> {
  return searchFromGraph({
    entityType: 'listItem',
    fields: [],
    getIds: (r: any) => (r.value as IGraphSearchResult[]).map(h => h.hitsContainers.map(i => i.hits ? i.hits.filter(v => v.hitId).map(v => v.hitId as string) : undefined).filter(v => v) as string[][]).flat(2) ?? [],
  }, parameters);
}

const searchFromGraph = async (sourceParameters: ISearchFromGraphSourceParameters, parameters: ISearchParameters): Promise<IBatchResult[]> => {
  // キー 'Graph' で格納されている値を取得する。
  // この値がアクセストークン
  const token = parameters.res['Graph'];
  const getClient = (): Client => {
    return createGraphClientAttane(token);
  }

  const getRequest: (context: ISearchContext) => BatchRequestData = (context) => {
    const body = JSON.stringify(createSearchQueryWithSearchWords(
      MAX_RESULT_GRAPH + 1,
      context.page,
      sourceParameters.entityType,
      [],
      parameters.conditionKeywords,
      '',
    ));
    parameters.logger?.verbose(`body: ${body}`);
    return ({
      id: '',
      url: 'https://graph.microsoft.com/v1.0/search/query',
      method: 'POST',
      headers: {
        'content-type': 'application/json',
      },
      body,
    });
  };

  const getPagingContext = (context: ISearchContext, r: any[]) => {
    const filtered = r.filter(v => v.GUID);

    if (filtered.length <= MAX_RESULT_GRAPH) {
      return undefined;
    }
    const pageLast = filtered[MAX_RESULT_GRAPH - 1];
    return {
      id: pageLast.ID,
      modified: toUTC(`${pageLast.Modified}${context.properties.timezoneOffset ?? ''}`),
    };
  }

  return (await executeAll({
    process: 'graph',
    //client: getClient(),
    //endPoint: 'https://graph.microsoft.com/v1.0',
    //token,
    context: parameters.properties.local,
    getClient: (params: Dictionary<string, string>) => getClient(),
    getRequest: getRequest,
    getIds: sourceParameters.getIds,
    getPagingContext,
    getUniqueKey: (context: ISearchContext) => `${context.page}`,
  })).map(result => {
    if (!result.success && result?.error?.statusCode === HttpStatusCode.Forbidden) {
      // 権限がなくて取得できなかった場合は、単に見つからなかったことにする
      parameters.logger?.verbose(`Forbidden: ${result?.error?.message}`);
      return {
        ...result,

        success: true,
        error: undefined,
        hasNext: false,
        ids: [],
      };
    }


    const exceedsLength = (result.ids?.length ?? 0) > MAX_RESULT_GRAPH;
    const hasNext = exceedsLength && result.page < MAX_PAGE_GRAPH
    return {
      ...result,
      hasNext,
      ids: exceedsLength ? result.ids?.slice(0, MAX_RESULT_GRAPH) : result.ids,
    };
  });
}

export function createSearchQueryWithSearchWords(
  /** ページサイズ */
  pageSize: number,
  /** ページ番号(1起点) */
  page: number,
  /** 検索対象のエンティティ */
  entityType: string,
  /** 検索対象のフィールド */
  fields: string[],
  /** 検索条件 */
  condition: ISearchConditionTree,
  /** 追加のフィルタ */
  additionalFilter: ISearchConditionTree | undefined,
): ISearchQuery {
  const queryString = flatten(additionalFilter ? { operator: 'And', operand: [ condition, additionalFilter ] } : condition);

  return {
    requests: [
      {
        entityTypes: [entityType],
        query: {
          queryString,
        },
        from: (page - 1) * (pageSize - 1),
        size: pageSize,
      }
    ]
  };
}

/**
 * ツリーを式表現に変換します
 * @param condition ツリー
 * @param isRoot ルートかどうか
 * @returns ２項演算子によって結合された式
 */
export function flatten(condition: ISearchConditionTree, isRoot: boolean = true): string {
  if (typeof condition === 'string') {
    return condition;
  }
  // KQLにおける論理演算子は大文字で、前後にスペースを入れる
  const operatorKeyword = ` ${condition.operator.toUpperCase()} `;

  const joined = condition.operand.map(op => flatten(op, false)).join(operatorKeyword);
  // ルートの場合と項が1つの場合は括弧が不要
  return isRoot || condition.operand.length <= 1 ? joined : `(${ joined })`;
}
