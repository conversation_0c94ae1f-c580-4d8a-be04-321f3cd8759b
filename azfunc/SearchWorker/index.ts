import { AzureFunction, Context, Logger } from '@azure/functions';
import searchFromSpo from './searchFromSpo';
import { searchFromGraphChat, searchFromGraphFile, searchFromGraphMail } from './searchFromGraph';
import Dictionary from '../types/Dictionary';
import ISearchParameters, { ISearchConditionTree } from '../types/ISearchParameters';
import { eq, when } from '../utilities/switch';
import { getTable, ITableMessage, ITableOperations } from './tableStorage';
import sleep from '../utilities/sleep';
import HttpStatusCode from '../types/HttpStatusCode';
import IBatchResult from '../types/IBatchResult';

/**
 * キューから取り出したメッセージ
 */
export type IQueueMessage = {
  userId: string,
  reqId: string,
  pid: string,
  kind: string,
  globalProperties: Dictionary<string, string>,
  properties: Dictionary<string, string>[],
  /**
   * キューに再投入された回数。初期投入時は空。
   */
  requeueCount?: number,
}

const MaxDequeueCount = process.env?.RetryCount ? Number.parseInt(process.env?.RetryCount) : 5;

const parseJson = (input: string, fallback: (e: any) => any): any =>
{
  if (input) {
    try {
      return JSON.parse(input);
    } catch (e) {
      return fallback(e);
    }
  }
  return undefined;
}

export function checkAbortProcessRequired(tableMessage: ITableMessage | undefined, message: IQueueMessage, logger: Logger) {
  if (tableMessage?.ReqId?.value !== message.reqId) {
    logger.info('検索要求が存在しないかすでに別の検索要求が行われているため、処理を中断します');
    return true;
  }
  if(tableMessage?.State === 'Cancelled') {
    logger.info('検索要求がキャンセルされているため、処理を中断します');
    return true;
  }
  return false;
}

export const main: AzureFunction = async function(context: Context, msg: IQueueMessage | {data: IQueueMessage}): Promise<void> {
  const logger = context.log;
  // beef 5では直接書かれるが、4ではmsgの中に含まれるため、両方対応可能にしておく
  const message = (msg as {data: IQueueMessage}).data ?? msg as IQueueMessage;

  logger.verbose("処理開始");

  const pid = message.pid
  logger.info(`Message(${pid}): ${JSON.stringify(message)}`);

  if (!message.userId || !message.reqId || !message.pid)
  {
    // リトライしても仕方ない、かつ、要求を更新できないので終了
    logger.error('要求メッセージに必要な項目が不足しています');
    return;
  }

  // connect to Azure Storage
  const connectionString = process.env.AzureWebJobsStorage;
  if (!connectionString) {
    logger.error('接続文字列が存在していません');
    throw new Error('connectionString is missing');
  }

  const table = getTable(connectionString);
  let tableMessage: ITableMessage | undefined = undefined;

  // 検索要求の結果をエラーとして更新
  try {
    logger.info(`Stage 1: TableStorage取得(${pid})`);
    tableMessage = await table.get(message.userId, 'request');
    // 取得した検索要求の検索IDとQueueStorageの検索IDが個なる場合は処理中止
    if(checkAbortProcessRequired(tableMessage, message, logger)) return;

    if (!tableMessage.Tokens) {
      throw new Error('TableStorageにトークンが設定されていません');
    }
    if (!tableMessage.ConditionKeywords) {
      throw new Error('TableStorageに検索条件が設定されていません');
    }
    const res = parseJson(tableMessage.Tokens, (e) => { throw new Error('TableStorageのトークンの形式が正しくありません'); }) as Dictionary<string, string>;
    const conditionKeywords = parseJson(tableMessage.ConditionKeywords, (e) => { throw new Error('TableStorageの検索条件の形式が正しくありません'); }) as ISearchConditionTree;

    logger.info(`検索キーワード: ${tableMessage.ConditionKeywords}`);

    let searchParameters: ISearchParameters = {
      conditionKeywords,
      res,
      properties: {
        global: message.globalProperties,
        local: message.properties.map(prop => ({
          properties: prop,
          page: 1,
        })),
      },
      logger,
    };

    while (searchParameters.properties.local.length > 0) {

      logger.info(`Stage 2: 検索(${pid})`);
      const results = await search(message, searchParameters);

      logger.info(`Stage 3: TableStorage再取得(${pid})`);
      tableMessage = await table.get(message.userId, 'request');
      // 取得した検索要求の検索IDとQueueStorageの検索IDが個なる場合は処理中止
      if (checkAbortProcessRequired(tableMessage, message, logger)) return;

      logger.info(`検索結果:(${JSON.stringify(results.map(r => r.ids))})`);

      logger.info(`Stage 4: 結果登録(${pid})`);

      // ログ出力
      await registerResults(results, logger, table, message, msg, context);

      searchParameters = generateNextSearchParameters(results, searchParameters);
    }

    logger.verbose(`処理完了(${pid})`);
  } catch (e) {
    await handleError(e, context, message.requeueCount ?? 0, logger, tableMessage, message.userId, table);
  }
}

function generateNextSearchParameters(results: IBatchResult[], searchParameters: ISearchParameters) {
  const nextPages = results.map((r, index) => {
    const local = searchParameters.properties.local[index];
    // 次ページがあるもののみに限定し、localとpagingが対になるよう取得
    return {
      exist: r.success && r.hasNext,
      properties: local.properties,
      paging: r.success && r.hasNext ? r.pagingContext : undefined,
      page: local.page + 1,
    };
  }).filter(v => v.exist);

  searchParameters = {
    ...searchParameters,
    properties: {
      ...searchParameters.properties,
      local: nextPages.map(p => ({
        properties: p.properties,
        paging: p.paging,
        page: p.page,
      })),
    },
  };
  return searchParameters;
}

async function registerResults(results: IBatchResult[], logger: Logger, table: ITableOperations, message: IQueueMessage, msg: IQueueMessage | { data: IQueueMessage; }, context: Context) {
  results.filter(r => !r.success).forEach(r => {
    logError(logger, r.error, r.properties);
  });

  // recoverable以外のものは結果出力する
  await Promise.all(results.filter(r => r.success || !isRecoverable(r.error)).map(r => table.create(
    {
      partitionKey: message.userId,
      rowKey: `result-{${message.reqId}}-{${message.pid}-${r.uniqueKey}}`,
      ReqId: { type: 'Guid', value: message.reqId, },
      Pid: `${message.pid}-${r.uniqueKey}`,
      Kind: message.kind,
      Properties: JSON.stringify(r.properties),
      Ids: JSON.stringify(r.ids ?? []),
      HasNext: r.hasNext ?? false,
    }
  )
  ));

  // リトライ可能な部分を再書き込み
  if ((message.requeueCount ?? 0) < MaxDequeueCount - 1) {
    await sendRetry(results, message, msg, context);
  }
}
// kindプロパティが追加されていて、その中に検索結果を入れている
// AI searchでは検索要求として渡してないので検索は行われない
// message=searchProcessRequest searchParameters = 検索文字
async function search(message: IQueueMessage, searchParameters: ISearchParameters) {
  const process = when(message.kind)
    .on(eq('SPO'), () => searchFromSpo)
    .on(eq('Mail'), () => searchFromGraphMail)
    .on(eq('Chat'), () => searchFromGraphChat)
    .on(eq('File'), () => searchFromGraphFile)
    .otherwise(() => undefined);
  if (!process) {
    throw new Error(`未知のデータソース種別が与えられたため、処理できません: [kind=${message.kind}]`);
  }

  const results = await process(searchParameters);
  return results;
}

async function sendRetry(results: IBatchResult[], message: IQueueMessage, msg: IQueueMessage | { data: IQueueMessage; }, context: Context) {
  const retryables = results
    .filter(r => !r.success && isRecoverable(r.error))
    .map(r => r.properties);
  if (retryables.length > 0) {
    // 即時にリトライが行われるため、リトライ前に待つ
    const wait = 2 ** (message.requeueCount ?? 0);
    await sleep(1000 * wait);

    const retryMessage = (msg as { data: IQueueMessage}).data ?
    {
      ...msg,
      data: {
        ...message,
        properties: retryables,
        requeueCount: (message.requeueCount ?? 0) + 1,
      }
    } : {
      ...message,
      properties: retryables,
      requeueCount: (message.requeueCount ?? 0) + 1,
    };
    context.bindings.retryQueue = retryMessage;
  }
}

async function writeError(tableMessage: ITableMessage | undefined, userId: string, table: ITableOperations) {
  if (!tableMessage)
  {
    // TableStorageからレコードが取得できていなければ更新対象がないので更新しない
    return;
  }
  await table.update({
    partitionKey: userId,
    rowKey: 'request',
    State: 'Error',
  })
};

function isForbidden(e: any) {
  return e?.statusCode === HttpStatusCode.Forbidden;
}

function isRecoverable(e: any) {
  return HttpStatusCode.isRecoverable(e?.statusCode);
}

function shouldUpdateState(e: any, context: Context, requeueCount: number) {
  return !isRecoverable(e) || context.bindingData.dequeueCount === MaxDequeueCount || requeueCount === MaxDequeueCount - 1;
}

function logError(logger: Logger, e: any, customDimenstions: any) {
  if (isForbidden(e)) {
    if (customDimenstions) {
      logger.info(`Forbidden error: [${statusCodeMessage(e?.statusCode)}]`, customDimenstions);
    } else {
      logger.info(`Forbidden error: [${statusCodeMessage(e?.statusCode)}]`);
    }
  }
  if (isRecoverable(e)) {
    if (customDimenstions) {
      logger.warn(`Recoverable error: [${statusCodeMessage(e?.statusCode)}]`, customDimenstions);
    } else {
      logger.warn(`Recoverable error: [${statusCodeMessage(e?.statusCode)}]`);
    }
  } else {
    if (customDimenstions) {
      logger.error(`Unrecoverable error: [${statusCodeMessage(e?.statusCode, true)}message=${e?.message ?? JSON.stringify(e)}]`, customDimenstions);
    } else {
      logger.error(`Unrecoverable error: [${statusCodeMessage(e?.statusCode, true)}message=${e?.message ?? JSON.stringify(e)}]`);
    }
  }
}

async function handleError(e: any, context: Context, requeueCount: number, logger: Logger, tableMessage: ITableMessage | undefined, userId: string, table: ITableOperations) {
  logError(logger, e, undefined);
  if (shouldUpdateState(e, context, requeueCount)) {
    await writeError(tableMessage, userId, table);
  }
  if (isRecoverable(e)) {
     // 例外を投げることで再実行される
     throw e;
  } else {
    // 例外を投げないことで再実行されない
    return;
  }
}

function statusCodeMessage(statusCode: number | null | undefined, appendTrailingSeparator: boolean = false) {
  const trailingSeparator = appendTrailingSeparator ? ', ' : '';
  return statusCode ? `status code=${statusCode}${trailingSeparator}` : '';
}

export default main;
