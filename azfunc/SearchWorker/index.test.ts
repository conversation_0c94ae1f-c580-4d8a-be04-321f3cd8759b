import { mockLogger, createMockContext as mockContext } from '../mocks/context';
import { IQueueMessage, main } from ".";
import searchFromSpo from './searchFromSpo';
import { getTable, ITableMessage } from './tableStorage';
import { RestError } from '@azure/data-tables';
import sleep from '../utilities/sleep';
import IBatchResult from '../types/IBatchResult';

const logger = mockLogger;

function createMockContext(dequeueCount: number = 1) {
  return mockContext({
    bindingData: {
      dequeueCount,
    },
    bindings: {},
  });
}
const getTableGet = jest.fn<Promise<ITableMessage | null>, any[]>();
const getTableCreate = jest.fn<Promise<void>, [ITableMessage]>();
const getTableUpdate = jest.fn<Promise<void>, [ITableMessage]>();
jest.mock("./searchFromSpo");
jest.mock('../utilities/sleep');

const searchFromSpoMock = (searchFromSpo as jest.MockedFunction<typeof searchFromSpo>);
const sleepMock = (sleep as jest.MockedFunction<typeof sleep>);

describe('main', () => {
  beforeEach(() => {
    (getTable as jest.Mock) = jest.fn().mockReturnValue({
      get: getTableGet as (partitionKey: string, rowKey: string) => Promise<ITableMessage>,
      create: getTableCreate as (msg: ITableMessage) => Promise<void>,
      update: getTableUpdate as (msg: ITableMessage) => Promise<void>,
    });

    logger.mockClear();
    getTableGet.mockClear();
    getTableGet.mockResolvedValue({
      partitionKey: 'testUser',
      rowKey: 'request',
      ReqId: { type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
      Pid: '11111111-1111-1111-1111-111111111112',
      ConditionKeywords: '{"operator":"And","operand":["abc","def"]}',
      State: 'InProgress',
      Tokens: '{"Spo":"spoToken"}',
    });
    getTableCreate.mockClear();
    getTableUpdate.mockClear();
    searchFromSpoMock.mockClear();
    searchFromSpoMock.mockResolvedValue([{
      success: true,
      page: 1,
      properties: { 'site': 'theSite', 'list': 'theList'},
      uniqueKey: 'unique',
      ids: ['result1', 'result2'],
    }]);

    process.env['AzureWebJobsStorage'] = 'connectionString';
  });

  describe('pre stage 1', () => {
    // ユーザーIDが指定されていない場合はエラー
    it('shows error when message does not have userId', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        //userId: 'testUser',
        properties: [{ 'site': 'theSite', 'list': 'theList'}],
      } as any as IQueueMessage

      const context = createMockContext();

      // act
      const result = main(context, {data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenCalledWith(expect.stringContaining('要求メッセージに必要な項目が不足しています'));
    });

    // 要求IDが指定されていない場合はエラー
    it('shows error when message does not have reqId', async () => {
      // arrange
      const message: IQueueMessage = {
        //reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        properties: [{ 'site': 'theSite', 'list': 'theList'}],
      } as any as IQueueMessage

      const context = createMockContext();

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenCalledWith(expect.stringContaining('要求メッセージに必要な項目が不足しています'));
    });

    // 処理IDが指定されていない場合はエラー
    it('shows error when message does not have pid', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        //pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        properties: [{ 'site': 'theSite', 'list': 'theList'}],
      } as any as IQueueMessage

      const context = createMockContext();

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenCalledWith(expect.stringContaining('要求メッセージに必要な項目が不足しています'));
    });
    // ストレージへの接続文字列が指定されていない場合はエラー
    it('shows error when connection string env is not defined', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      delete process.env.AzureWebJobsStorage;

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).rejects.toThrow('connectionString is missing');
    });

  });

  describe('stage 1', () => {
    // 検索要求が存在しない場合はエラー
    it('shows error when search request does not exist', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      getTableGet.mockClear();
      getTableGet.mockResolvedValue(null);

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenCalledWith(expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining(`Stage 1`));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining('検索要求が存在しないかすでに別の検索要求が行われているため、処理を中断します'));
    });

    // 検索要求がキャンセルされていた場合はエラー
    it('shows error when search request has been cancelled', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      getTableGet.mockClear();
      getTableGet.mockResolvedValue({
        partitionKey: 'testUser',
        rowKey: 'request',
        ReqId: { type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112',
        ConditionKeywords: '{"operator":"And","operand":["abc","def"]}',
        State: 'Cancelled',
        Tokens: '{"Spo":"spoToken"}',
      });

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenCalledWith(expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining(`Stage 1`));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining('検索要求がキャンセルされているため、処理を中断します'));
    });

    // トークンが存在しない場合はエラー
    it('shows error when search request does not have tokens', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      getTableGet.mockClear();
      getTableGet.mockResolvedValue({
        partitionKey: 'testUser',
        rowKey: 'request',
        ReqId: { type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112',
        ConditionKeywords: '{"operator":"And","operand":["abc","def"]}',
        State: 'InProgress',
        //Tokens: '{"Spo":"spoToken"}',
      });

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('TableStorageにトークンが設定されていません'));
    });

    // 検索キーワードが存在しない場合はエラー
    it('shows error when search request does not have keywords', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      getTableGet.mockClear();
      getTableGet.mockResolvedValue({
        partitionKey: 'testUser',
        rowKey: 'request',
        ReqId: { type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112',
        // ConditionKeywords: '["abc","def"]',
        State: 'InProgress',
        Tokens: '{"Spo":"spoToken"}',
      });

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('Unrecoverable error'));
    });

    // トークンの形式が正しくない場合はエラー
    it('shows error when search tokens of the request is malformatted', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      getTableGet.mockClear();
      getTableGet.mockResolvedValue({
        partitionKey: 'testUser',
        rowKey: 'request',
        ReqId: { type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112',
        ConditionKeywords: '{"operator":"And","operand":["abc","def"]}',
        State: 'InProgress',
        Tokens: '{"Spo":"spoToken"',
      });

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('Unrecoverable error'));
    });

    // 検索条件の形式が正しくない場合はエラー
    it('shows error when search keywords of the request is malformatted', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      getTableGet.mockClear();
      getTableGet.mockResolvedValue({
        partitionKey: 'testUser',
        rowKey: 'request',
        ReqId: { type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112',
        ConditionKeywords: '{"operator":"And","operand":["abc","def"',
        State: 'InProgress',
        Tokens: '{"Spo":"spoToken"}',
      });

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('Unrecoverable error'));
    });
  });

  describe('stage 2', () => {
    // 処理区分が正しくない場合はエラー
    it('shows error when search kind of the message is unknown', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'Other',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      getTableGet.mockClear();
      getTableGet.mockResolvedValue({
        partitionKey: 'testUser',
        rowKey: 'request',
        ReqId: { type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112',
        ConditionKeywords: '{"operator":"And","operand":["abc","def"]}',
        State: 'InProgress',
        Tokens: '{"Spo":"spoToken"}',
      });

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('検索キーワード'));
      expect(logger).toHaveBeenNthCalledWith(5, expect.stringContaining('Stage 2'));
      expect(logger).toHaveBeenNthCalledWith(6, expect.stringContaining('未知のデータソース種別が与えられたため、処理できません: [kind=Other]'));
    });
  });

  describe('stage 3', () => {
    // 検索後に検索要求が存在しない場合はエラー
    it('shows error when search request does not exist after the search', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      getTableGet.mockClear();
      getTableGet.mockResolvedValueOnce({
        partitionKey: 'testUser',
        rowKey: 'request',
        ReqId: { type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112',
        ConditionKeywords: '{"operator":"And","operand":["abc","def"]}',
        State: 'InProgress',
        Tokens: '{"Spo":"spoToken"}',
      });
      getTableGet.mockResolvedValue(null);

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('検索キーワード'));
      expect(logger).toHaveBeenNthCalledWith(5, expect.stringContaining('Stage 2'));
      expect(logger).toHaveBeenNthCalledWith(6, expect.stringContaining('Stage 3'));
      expect(logger).toHaveBeenNthCalledWith(7, expect.stringContaining('検索要求が存在しないかすでに別の検索要求が行われているため、処理を中断します'));
    });

    // 検索後に検索要求がキャンセルされていた場合はエラー
    it('shows error when search request has been cancelled after the search', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      getTableGet.mockClear();
      getTableGet.mockResolvedValueOnce({
        partitionKey: 'testUser',
        rowKey: 'request',
        ReqId: { type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112',
        ConditionKeywords: '{"operator":"And","operand":["abc","def"]}',
        State: 'InProgress',
        Tokens: '{"Spo":"spoToken"}',
      });
      getTableGet.mockResolvedValue({
        partitionKey: 'testUser',
        rowKey: 'request',
        ReqId: { type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112',
        ConditionKeywords: '{"operator":"And","operand":["abc","def"]}',
        State: 'Cancelled',
        Tokens: '{"Spo":"spoToken"}',
      });

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('検索キーワード'));
      expect(logger).toHaveBeenNthCalledWith(5, expect.stringContaining('Stage 2'));
      expect(logger).toHaveBeenNthCalledWith(6, expect.stringContaining('Stage 3'));
      expect(logger).toHaveBeenNthCalledWith(7, expect.stringContaining('検索要求がキャンセルされているため、処理を中断します'));
    });
  });

  describe('stage 4 success', () => {
    // 検索登録される
    it('creates the table entry after the search', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('検索キーワード'));
      expect(logger).toHaveBeenNthCalledWith(5, expect.stringContaining('Stage 2'));
      expect(logger).toHaveBeenNthCalledWith(6, expect.stringContaining('Stage 3'));
      expect(logger).toHaveBeenNthCalledWith(7, expect.stringContaining('検索結果'));
      expect(logger).toHaveBeenNthCalledWith(8, expect.stringContaining('Stage 4'));
      expect(getTableCreate).toHaveBeenCalledWith({
        partitionKey: 'testUser',
        rowKey: 'result-{11111111-1111-1111-1111-111111111111}-{11111111-1111-1111-1111-111111111112-unique}',
        ReqId: {type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112-unique',
        Kind: 'SPO',
        Properties: '{"site":"theSite","list":"theList"}',
        Ids: '["result1","result2"]',
        HasNext: false,
      });
      expect(getTableUpdate).toBeCalledTimes(0);
    });

    // 次ページも取得する
    it('proceeds to the next page when the search returns hasNext', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [
          {'site': 'theSite1', 'list': 'theList1'},
          {'site': 'theSite2', 'list': 'theList2'},
          {'site': 'theSite3', 'list': 'theList3'}
        ],
      }

      const context = createMockContext();

      searchFromSpoMock.mockResolvedValueOnce([
        {
          success: true,
          page: 1,
          properties: {'site': 'theSite1', 'list': 'theList1'},
          ids: ['aaa', 'bbb', 'ccc'],
          uniqueKey: 'unique1-1',
          hasNext: false,
        },
        {
          success: true,
          page: 1,
          properties: {'site': 'theSite2', 'list': 'theList2'},
          ids: ['ddd', 'eee', 'fff'],
          uniqueKey: 'unique2-1',
          hasNext: false,
        },
        {
          success: true,
          page: 1,
          properties: {'site': 'theSite3', 'list': 'theList3'},
          ids: ['ggg', 'hhh', 'iii'],
          pagingContext: {"id": "iii"},
          uniqueKey: 'unique3-1',
          hasNext: true,
        }
      ]);
      searchFromSpoMock.mockResolvedValueOnce([
        {
          success: true,
          page: 1,
          error: { statusCode: 500},
          properties: {'site': 'theSite3', 'list': 'theList3'},
          ids: ['jjj', 'kkk', 'lll'],
          uniqueKey: 'unique3-2',
          hasNext: false,
        }
      ]);

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('検索キーワード'));
      expect(logger).toHaveBeenNthCalledWith(5, expect.stringContaining('Stage 2'));
      expect(logger).toHaveBeenNthCalledWith(6, expect.stringContaining('Stage 3'));
      expect(logger).toHaveBeenNthCalledWith(7, expect.stringContaining('検索結果'));
      expect(logger).toHaveBeenNthCalledWith(8, expect.stringContaining('Stage 4'));
      expect(logger).toHaveBeenNthCalledWith(9, expect.stringContaining('Stage 2'));
      expect(logger).toHaveBeenNthCalledWith(10, expect.stringContaining('Stage 3'));
      expect(logger).toHaveBeenNthCalledWith(11, expect.stringContaining('検索結果'));
      expect(logger).toHaveBeenNthCalledWith(12, expect.stringContaining('Stage 4'));

      expect(searchFromSpoMock).toBeCalledTimes(2);
      expect(searchFromSpoMock.mock.calls[0][0].properties.local).toEqual([
        { properties: {'site': 'theSite1', 'list': 'theList1'}, page: 1, },
        { properties: {'site': 'theSite2', 'list': 'theList2'}, page: 1, },
        { properties: {'site': 'theSite3', 'list': 'theList3'}, page: 1, },
      ]);
      expect(searchFromSpoMock.mock.calls[1][0].properties.local).toEqual([
        {
          properties: {'site': 'theSite3', 'list': 'theList3'},
          paging: {'id': 'iii'},
          page: 2,
        },
      ]);

      expect(getTableCreate).toHaveBeenNthCalledWith(1, {
        partitionKey: 'testUser',
        rowKey: 'result-{11111111-1111-1111-1111-111111111111}-{11111111-1111-1111-1111-111111111112-unique1-1}',
        ReqId: {type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112-unique1-1',
        Kind: 'SPO',
        Properties: '{"site":"theSite1","list":"theList1"}',
        Ids: '["aaa","bbb","ccc"]',
        HasNext: false,
      });
      expect(getTableCreate).toHaveBeenNthCalledWith(2, {
        partitionKey: 'testUser',
        rowKey: 'result-{11111111-1111-1111-1111-111111111111}-{11111111-1111-1111-1111-111111111112-unique2-1}',
        ReqId: {type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112-unique2-1',
        Kind: 'SPO',
        Properties: '{"site":"theSite2","list":"theList2"}',
        Ids: '["ddd","eee","fff"]',
        HasNext: false,
      });
      expect(getTableCreate).toHaveBeenNthCalledWith(3, {
        partitionKey: 'testUser',
        rowKey: 'result-{11111111-1111-1111-1111-111111111111}-{11111111-1111-1111-1111-111111111112-unique3-1}',
        ReqId: {type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112-unique3-1',
        Kind: 'SPO',
        Properties: '{"site":"theSite3","list":"theList3"}',
        Ids: '["ggg","hhh","iii"]',
        HasNext: true,
      });
      expect(getTableCreate).toHaveBeenNthCalledWith(4, {
        partitionKey: 'testUser',
        rowKey: 'result-{11111111-1111-1111-1111-111111111111}-{11111111-1111-1111-1111-111111111112-unique3-2}',
        ReqId: {type: 'Guid', value: '11111111-1111-1111-1111-111111111111'},
        Pid: '11111111-1111-1111-1111-111111111112-unique3-2',
        Kind: 'SPO',
        Properties: '{"site":"theSite3","list":"theList3"}',
        Ids: '["jjj","kkk","lll"]',
        HasNext: false,
      });
      expect(getTableUpdate).toBeCalledTimes(0);
    });
  });

  describe('stage 4 error', () => {
    // リトライ可能なエラーの場合例外を投げる
    it('throws exception if retriable error occurs', async() => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      searchFromSpoMock.mockImplementation(() => {
        throw {
          message: 'too many requests',
          statusCode: 429
        } as RestError;
      });

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).rejects.toEqual({message: 'too many requests', statusCode: 429});

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('検索キーワード'));
      expect(logger).toHaveBeenNthCalledWith(5, expect.stringContaining('Stage 2'));
      expect(logger).toHaveBeenNthCalledWith(6, expect.stringContaining('Recoverable error: [status code=429]'));
      expect(getTableUpdate).toBeCalledTimes(0);
    });

    // 5回目がリトライ可能なエラーの場合処理を中断する
    it('gives up if retriable error occurs at fifth attempt', async() => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext(5);
      searchFromSpoMock.mockImplementation(() => {
        throw {
          message: 'too many requests',
          statusCode: 429
        } as RestError;
      });

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).rejects.toEqual({message: 'too many requests', statusCode: 429});

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('検索キーワード'));
      expect(logger).toHaveBeenNthCalledWith(5, expect.stringContaining('Stage 2'));
      expect(logger).toHaveBeenNthCalledWith(6, expect.stringContaining('Recoverable error: [status code=429]'));
      expect(getTableUpdate).toHaveBeenCalledWith({
        partitionKey: 'testUser',
        rowKey: 'request',
        State: 'Error',
      });
    });

    // リトライ可能でないRestエラーの場合処理を中断する
    it('gives up if unretriable REST error occurs', async() => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      searchFromSpoMock.mockImplementation(() => {
        throw {
          message: 'unauthorized',
          statusCode: 401
        } as RestError;
      });

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('検索キーワード'));
      expect(logger).toHaveBeenNthCalledWith(5, expect.stringContaining('Stage 2'));
      expect(logger).toHaveBeenNthCalledWith(6, expect.stringContaining('Unrecoverable error: [status code=401, message=unauthorized]'));
      expect(getTableUpdate).toHaveBeenCalledWith({
        partitionKey: 'testUser',
        rowKey: 'request',
        State: 'Error',
      });
    });

    // Restでないエラーの場合処理を中断する
    it('gives up if unretriable non-REST error occurs', async() => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      searchFromSpoMock.mockImplementation(() => {
        throw new Error('something wrong');
      });

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('検索キーワード'));
      expect(logger).toHaveBeenNthCalledWith(5, expect.stringContaining('Stage 2'));
      expect(logger).toHaveBeenNthCalledWith(6, expect.stringContaining('Unrecoverable error: [message=something wrong]'));
      expect(getTableUpdate).toHaveBeenCalledWith({
        partitionKey: 'testUser',
        rowKey: 'request',
        State: 'Error',
      });
    });

    // Errorでない文字列が投げられた場合処理を中断する
    it('gives up if non-error is thrown', async() => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [{'site': 'theSite', 'list': 'theList'}],
      }

      const context = createMockContext();
      searchFromSpoMock.mockImplementation(() => {
        throw 'something wrong';
      });

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('検索キーワード'));
      expect(logger).toHaveBeenNthCalledWith(5, expect.stringContaining('Stage 2'));
      expect(logger).toHaveBeenNthCalledWith(6, expect.stringContaining('Unrecoverable error: [message="something wrong"]'));
      expect(getTableUpdate).toHaveBeenCalledWith({
        partitionKey: 'testUser',
        rowKey: 'request',
        State: 'Error',
      });
    });
  });

  describe('when some of the result are unrecoverable error', () => {
    // 個々の検索がリトライ不可のエラーを出した場合でも検索は成功する
    it('logs the error but finishes the process', async() => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [
          {'site': 'theSite1', 'list': 'theList1'},
          {'site': 'theSite2', 'list': 'theList2'},
          {'site': 'theSite3', 'list': 'theList3'},
        ],
      }

      const context = createMockContext();
      searchFromSpoMock.mockImplementation((p) => Promise.resolve<IBatchResult[]>([
        {
          success: true,
          page: 1,
          properties: p.properties.local[0].properties,
          ids: ['aaa', 'bbb', 'ccc'],
        },
        {
          success: false,
          page: 1,
          error: { statusCode: 400},
          properties: p.properties.local[1].properties,
        },
        {
          success: false,
          page: 1,
          error: { statusCode: 500},
          properties: p.properties.local[2].properties,
        }
      ]));

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(logger).toHaveBeenNthCalledWith(1, expect.stringContaining('処理開始'));
      expect(logger).toHaveBeenNthCalledWith(2, expect.stringContaining('Message(11111111-1111-1111-1111-111111111112)'));
      expect(logger).toHaveBeenNthCalledWith(3, expect.stringContaining('Stage 1'));
      expect(logger).toHaveBeenNthCalledWith(4, expect.stringContaining('検索キーワード'));
      expect(logger).toHaveBeenNthCalledWith(5, expect.stringContaining('Stage 2'));
      expect(logger).toHaveBeenNthCalledWith(6, expect.stringContaining('Stage 3'));
      expect(logger).toHaveBeenNthCalledWith(7, expect.stringContaining('検索結果'));
      expect(logger).toHaveBeenNthCalledWith(8, expect.stringContaining('Stage 4'));
      expect(logger).toHaveBeenNthCalledWith(9, expect.stringContaining('Unrecoverable error: [status code=400'), expect.objectContaining({'site': 'theSite2', 'list': 'theList2'}));
      expect(logger).toHaveBeenNthCalledWith(10, expect.stringContaining('Unrecoverable error: [status code=500'), expect.objectContaining({'site': 'theSite3', 'list': 'theList3'}));
      expect(getTableUpdate).toBeCalledTimes(0);
      expect(getTableCreate).toBeCalledTimes(3);

    });


  });
  describe('when some of the result are recoverable error', () => {
    beforeEach(() => {
      sleepMock.mockClear();
    });

    it('waits 1 second for the 1st attempt', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [
          {'site': 'theSite1', 'list': 'theList1'},
          {'site': 'theSite2', 'list': 'theList2'},
          {'site': 'theSite3', 'list': 'theList3'},
        ],
      }

      const context = createMockContext();
      searchFromSpoMock.mockImplementation((p) => Promise.resolve<IBatchResult[]>([
        {
          success: true,
          page: 1,
          properties: p.properties.local[0].properties,
          ids: ['aaa', 'bbb', 'ccc'],
        },
        {
          success: false,
          page: 1,
          error: { statusCode: 409},
          properties: p.properties.local[1].properties,
        },
        {
          success: false,
          page: 1,
          error: { statusCode: 500},
          properties: p.properties.local[2].properties,
        }
      ]));

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(sleepMock).toHaveBeenNthCalledWith(1, 1000);
      expect(context.bindings.retryQueue.data.properties).toEqual([{'site': 'theSite2', 'list': 'theList2'}]);
      expect(context.bindings.retryQueue.data.requeueCount).toBe(1);
    });

    it('waits 8 seconds for the 4th attempt', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [
          {'site': 'theSite1', 'list': 'theList1'},
          {'site': 'theSite2', 'list': 'theList2'},
          {'site': 'theSite3', 'list': 'theList3'},
        ],
        requeueCount: 3,
      }

      const context = createMockContext();
      searchFromSpoMock.mockImplementation((p) => Promise.resolve<IBatchResult[]>([
        {
          success: true,
          page: 1,
          properties: p.properties.local[0].properties,
          ids: ['aaa', 'bbb', 'ccc'],
        },
        {
          success: false,
          page: 1,
          error: { statusCode: 409},
          properties: p.properties.local[1].properties,
        },
        {
          success: false,
          page: 1,
          error: { statusCode: 500},
          properties: p.properties.local[2].properties,
        }
      ]));

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(sleepMock).toHaveBeenNthCalledWith(1, 8000);
      expect(context.bindings.retryQueue.data.properties).toEqual([{'site': 'theSite2', 'list': 'theList2'}]);
      expect(context.bindings.retryQueue.data.requeueCount).toBe(4);
    });

    it('gives up for the 5th attempt', async () => {
      // arrange
      const message: IQueueMessage = {
        reqId: '11111111-1111-1111-1111-111111111111',
        pid: '11111111-1111-1111-1111-111111111112',
        kind: 'SPO',
        userId: 'testUser',
        globalProperties: {'site': 'theGlobal' },
        properties: [
          {'site': 'theSite1', 'list': 'theList1'},
          {'site': 'theSite2', 'list': 'theList2'},
          {'site': 'theSite3', 'list': 'theList3'},
        ],
        requeueCount: 4,
      }

      const context = createMockContext();
      searchFromSpoMock.mockImplementation((p) => Promise.resolve<IBatchResult[]>([
        {
          success: true,
          page: 1,
          properties: p.properties.local[0].properties,
          ids: ['aaa', 'bbb', 'ccc'],
        },
        {
          success: false,
          page: 1,
          error: { statusCode: 409},
          properties: p.properties.local[1].properties,
        },
        {
          success: false,
          page: 1,
          error: { statusCode: 500},
          properties: p.properties.local[2].properties,
        }
      ]));

      // act
      const result = main(context, { data: message });

      // assert
      await expect(result).resolves.toBeUndefined();

      expect(sleepMock).not.toHaveBeenCalled();
      expect(context.bindings?.retryQueue).toBeUndefined();
    });
  });
});
