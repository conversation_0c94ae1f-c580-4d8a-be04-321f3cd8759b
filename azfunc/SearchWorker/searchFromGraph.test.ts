import ISearchParameters, { ISearchConditionTree } from "../types/ISearchParameters";
import { ISharePointListsResponse } from "../types/ISharePointListResponse";
import { searchFromGraphMail, searchFromGraphChat, searchFromGraphFile, flatten } from "./searchFromGraph";
import { Client } from "@microsoft/microsoft-graph-client";
import { executeAll } from "../utilities/api";
import { parseSharePointPath } from "../utilities/url";
import { RequestInit } from "node-fetch";
import { MAX_PAGE_GRAPH } from "../utilities/lib";

jest.mock('../utilities/api');
jest.mock('../utilities/graph');
jest.mock('../utilities/url');
jest.mock('@microsoft/microsoft-graph-client');

const fetchListResult = jest.fn<Promise<ISharePointListsResponse>, []>();

const executeAllMock = executeAll as jest.MockedFunction<typeof executeAll>;
const parseSharePointPathMock = parseSharePointPath as jest.MockedFunction<typeof parseSharePointPath>;

let containedToken: string | undefined;

describe('flatten', () => {
  describe('0 level', () => {
    it('returns single keyword as is', () => {
      // arrange
      const source: ISearchConditionTree = 'abc';

      // act
      const result = flatten(source);

      // assert
      expect(result).toBe('abc');
    })
  });

  describe('1 level', () => {
    it('returns single operand as is', () => {
      // arrange
      const source: ISearchConditionTree = { operator: 'And', operand: ['abc'] };

      // act
      const result = flatten(source);

      // assert
      expect(result).toBe('abc');
    });

    it('returns binary operator expression for 1-level tree', () => {
      // arrange
      const source: ISearchConditionTree = { operator: 'And', operand: ['abc', 'def'] };

      // act
      const result = flatten(source);

      // assert
      expect(result).toBe('abc AND def');

    });
  });

  describe('multiple levels', () => {
    it('returns single operand as is', () => {
      // arrange
      const source: ISearchConditionTree = { operator: 'And', operand: [{ operator: 'Or', operand: ['abc'] }, { operator: 'And', operand: ['def']}] };

      // act
      const result = flatten(source);

      // assert
      expect(result).toBe('abc AND def');
    });
    it('returns expression using paren', () => {
      // arrange
      const source: ISearchConditionTree = { operator: 'And', operand: [{ operator: 'Or', operand: ['abc', 'def'] }, { operator: 'Or', operand: ['ghi', 'jkl']}] };

      // act
      const result = flatten(source);

      // assert
      expect(result).toBe('(abc OR def) AND (ghi OR jkl)');
    })
  });
});

describe('searchFromGraphMail', () => {

  beforeEach(() => {
    containedToken = undefined;
    executeAllMock.mockClear();
    fetchListResult.mockClear();
  });

  describe('the passed getRequest callback', () => {
    it('returns request', async() => {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
        },
      };
      const theClient = {} as Client;
      executeAllMock.mockResolvedValue([{
        page: 1,
        success: true,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromGraphMail(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedRequest = param.getRequest(param.context[0]);

      // assert
      expect(resolvedRequest.id).toBe('');
      expect(resolvedRequest.url).toBe('https://graph.microsoft.com/v1.0/search/query');
      expect((resolvedRequest as RequestInit).method).toBe('POST');
      expect((resolvedRequest as RequestInit).headers).toEqual({
        'content-type': 'application/json'
      });
      expect((resolvedRequest as RequestInit).body as string).toBe('{"requests":[{"entityTypes":["message"],"query":{"queryString":"a AND b"},"from":0,"size":21}]}');
    });
  });

  describe('the passed getIds callback', () => {
    it('returns mail ids from fetched list', async () =>
    {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
        },
      };
      executeAllMock.mockResolvedValue([{
        page: 1,
        success: true,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromGraphMail(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedIds = param.getIds(
        {
          value: [
            {
              hitsContainers: [
                {
                  hits: [
                    {
                      hitId: 'Guid1',
                      Title: 'Title1',
                    },
                    {
                      hitId: 'Guid2',
                      Title: 'Title2',
                    }
                  ]
                },
              ]
            }
          ]
        });

      // assert
      expect(resolvedIds).toEqual(['Guid1', 'Guid2']);
    });

    it ('returns empty array from empty fetched list', async () => {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
        },
      };
      executeAllMock.mockResolvedValue([{
        page: 1,
        success: true,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromGraphMail(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedIds = param.getIds({value: []});

      // assert
      expect(resolvedIds).toEqual([]);
    });

    it ('returns only items that have Guid', async () => {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1,}],
        },
      };
      executeAllMock.mockResolvedValue([{
        page: 1,
        success: true,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromGraphMail(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedIds = param.getIds(
        {
          value: [
            {
              hitsContainers: [
                {
                  hits: [
                    {
                      Title: 'Title1',
                    },
                    {
                      hitId: 'Guid2',
                      Title: 'Title2',
                    }
                  ]
                },
              ]
            }
          ]
        });

      // assert
      expect(resolvedIds).toEqual(['Guid2']);
    });
  });

  it('passes the received parameters to the graph client', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: ['Guid1', 'Guid2'],
    }]);

    // act
    const result = searchFromGraphMail(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: ['Guid1', 'Guid2'],
      hasNext: false,
    }]);

    const param = executeAllMock.mock.calls[0][0];
    expect(param.process).toBe('graph');
    // expect(param.endPoint).toBe('ga1/_api');
    // expect(param.token).toBe('spoToken');
    expect(param.context).toEqual([{
      properties: {
        site: 'e1',
        list: 'f1',
        category: 'g1',
      },
      page: 1,
    }]);
  });

  it('ignores the forbidden error returned from the graph client', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      page: 1,
      success: false,
      error: { statusCode: 403, message: 'Forbidden' },
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
    }]);

    // act
    const result = searchFromGraphMail(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: [],
      hasNext: false,
    }]);
  });

  it('returns hasNext when the result exceeds MAX_RESULT', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(201), (x, i) => `id_${i}`),
    }]);

    // act
    const result = searchFromGraphMail(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(20), (x, i) => `id_${i}`),
      hasNext: true,
    }]);
  });

  it('doesn\'t return hasNext when the page is the last one even if the result exceeds MAX_RESULT', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      page: MAX_PAGE_GRAPH,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(201), (x, i) => `id_${i}`),
    }]);

    // act
    const result = searchFromGraphMail(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      page: MAX_PAGE_GRAPH,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(20), (x, i) => `id_${i}`),
      hasNext: false,
    }]);
  });
});

describe('searchFromGraphChat', () => {

  beforeEach(() => {
    containedToken = undefined;
    executeAllMock.mockClear();
    fetchListResult.mockClear();
  });

  describe('the passed getRequest callback', () => {
    it('returns request', async() => {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
        },
      };
      executeAllMock.mockResolvedValue([{
        page: 1,
        success: true,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromGraphChat(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedRequest = param.getRequest(param.context[0]);

      // assert
      expect(resolvedRequest.id).toBe('');
      expect(resolvedRequest.url).toBe('https://graph.microsoft.com/v1.0/search/query');
      expect((resolvedRequest as RequestInit).method).toBe('POST');
      expect((resolvedRequest as RequestInit).headers).toEqual({
        'content-type': 'application/json'
      });
      expect((resolvedRequest as RequestInit).body as string).toBe('{"requests":[{"entityTypes":["chatMessage"],"query":{"queryString":"a AND b"},"from":0,"size":21}]}');
    });
  });

  describe('the passed getIds callback', () => {
    it('returns mail ids from fetched list', async () =>
    {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
        },
      };
      executeAllMock.mockResolvedValue([{
        page: 1,
        success: true,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromGraphChat(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedIds = param.getIds(
        {
          value: [
            {
              hitsContainers: [
                {
                  hits: [
                    {
                      hitId: 'Guid1',
                      resource: {
                        channelIdentity: {
                          teamId: 'Team1',
                          channelId: 'Channel1',
                        },
                        id: 'Id1',
                      }
                    },
                    {
                      hitId: 'Guid2',
                      resource: {
                        channelIdentity: {},
                        chatId: 'Chat2',
                        id: 'Id2',
                      },
                    }
                  ]
                },
              ]
            }
          ]
        });

      // assert
      expect(resolvedIds).toEqual(['team|Team1|Channel1|Id1', 'chat||Chat2|Id2']);
    });

    it ('returns empty array from empty fetched list', async () => {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
        },
      };
      executeAllMock.mockResolvedValue([{
        page: 1,
        success: true,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromGraphChat(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedIds = param.getIds({value: []});

      // assert
      expect(resolvedIds).toEqual([]);
    });

    it ('returns only items that have Guid', async () => {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1,}],
        },
      };
      executeAllMock.mockResolvedValue([{
        page: 1,
        success: true,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromGraphChat(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedIds = param.getIds(
        {
          value: [
            {
              hitsContainers: [
                {
                  hits: [
                    {
                      hitId: 'Guid1',
                      resource: {
                        channelIdentity: {
                          teamId: 'Team1',
                          channelId: 'Channel1',
                        },
                      }
                    },
                    {
                      hitId: 'Guid2',
                      resource: {
                        channelIdentity: {
                          teamId: 'Team2',
                        },
                        id: 'Id2',
                      }
                    },
                    {
                      hitId: 'Guid3',
                      resource: {
                        channelIdentity: {},
                        chatId: 'Chat3',
                      },
                    },
                    {
                      hitId: 'Guid4',
                      resource: {
                        channelIdentity: {},
                        id: 'Id4',
                      },
                    }
                  ]
                },
              ]
            }
          ]
        });

      // assert
      expect(resolvedIds).toEqual([]);
    });
  });

  it('passes the received parameters to the graph client', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: ['Guid1', 'Guid2'],
    }]);

    // act
    const result = searchFromGraphChat(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: ['Guid1', 'Guid2'],
      hasNext: false,
    }]);

    const param = executeAllMock.mock.calls[0][0];
    expect(param.process).toBe('graph');
    // expect(param.endPoint).toBe('ga1/_api');
    // expect(param.token).toBe('spoToken');
    expect(param.context).toEqual([{
      properties: {
        site: 'e1',
        list: 'f1',
        category: 'g1',
      },
      page: 1,
    }]);
  });

  it('ignores the forbidden error returned from the graph client', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      page: 1,
      success: false,
      error: { statusCode: 403, message: 'Forbidden' },
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
    }]);

    // act
    const result = searchFromGraphChat(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: [],
      hasNext: false,
    }]);
  });

  it('returns hasNext when the result exceeds MAX_RESULT', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(201), (x, i) => `id_${i}`),
    }]);

    // act
    const result = searchFromGraphChat(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(20), (x, i) => `id_${i}`),
      hasNext: true,
    }]);
  });

  it('doesn\'t return hasNext when the page is the last one even if the result exceeds MAX_RESULT', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      page: MAX_PAGE_GRAPH,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(201), (x, i) => `id_${i}`),
    }]);

    // act
    const result = searchFromGraphChat(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      page: MAX_PAGE_GRAPH,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(20), (x, i) => `id_${i}`),
      hasNext: false,
    }]);
  });
});


describe('searchFromGraphFile', () => {
  beforeEach(() => {
    containedToken = undefined;
    executeAllMock.mockClear();
    fetchListResult.mockClear();
  });

  describe('the passed getRequest callback', () => {
    it('returns request', async() => {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
        },
      };
      executeAllMock.mockResolvedValue([{
        page: 1,
        success: true,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromGraphFile(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedRequest = param.getRequest(param.context[0]);

      // assert
      expect(resolvedRequest.id).toBe('');
      expect(resolvedRequest.url).toBe('https://graph.microsoft.com/v1.0/search/query');
      expect((resolvedRequest as RequestInit).method).toBe('POST');
      expect((resolvedRequest as RequestInit).headers).toEqual({
        'content-type': 'application/json'
      });
      expect((resolvedRequest as RequestInit).body as string).toBe('{"requests":[{"entityTypes":["driveItem"],"query":{"queryString":"a AND b"},"from":0,"size":21}]}');
    });
  });

  describe('the passed getIds callback', () => {
    it('returns mail ids from fetched list', async () =>
    {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
        },
      };
      executeAllMock.mockResolvedValue([{
        page: 1,
        success: true,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromGraphFile(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedIds = param.getIds(
        {
          value: [
            {
              hitsContainers: [
                {
                  hits: [
                    {
                      hitId: 'Guid1',
                      resource: {
                        parentReference: {
                          siteId: 'Site1',
                        },
                        id: 'Id1',
                      }
                    },
                    {
                      hitId: 'Guid2',
                      resource: {
                        parentReference: {
                          siteId: 'Site2',
                        },
                        id: 'Id2',
                      },
                    }
                  ]
                },
              ]
            }
          ]
        });

      // assert
      expect(resolvedIds).toEqual(['Site1|Id1', 'Site2|Id2']);
    });

    it ('returns empty array from empty fetched list', async () => {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
        },
      };
      executeAllMock.mockResolvedValue([{
        page: 1,
        success: true,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromGraphFile(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedIds = param.getIds({value: []});

      // assert
      expect(resolvedIds).toEqual([]);
    });

    it ('returns only items that have Guid', async () => {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1,}],
        },
      };
      executeAllMock.mockResolvedValue([{
        page: 1,
        success: true,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromGraphFile(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedIds = param.getIds(
        {
          value: [
            {
              hitsContainers: [
                {
                  hits: [
                    {
                      hitId: 'Guid1',
                      resource: {
                        parentReference: {
                          siteId: 'Site1',
                        },
                      }
                    },
                    {
                      hitId: 'Guid2',
                      resource: {
                        parentReference: {
                        },
                        id: 'Id2',
                      }
                    }
                  ]
                },
              ]
            }
          ]
        });

      // assert
      expect(resolvedIds).toEqual([]);
    });
  });

  it('passes the received parameters to the graph client', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: ['Guid1', 'Guid2'],
    }]);

    // act
    const result = searchFromGraphFile(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: ['Guid1', 'Guid2'],
      hasNext: false,
    }]);

    const param = executeAllMock.mock.calls[0][0];
    expect(param.process).toBe('graph');
    // expect(param.endPoint).toBe('ga1/_api');
    // expect(param.token).toBe('spoToken');
    expect(param.context).toEqual([{
      properties: {
        site: 'e1',
        list: 'f1',
        category: 'g1',
      },
      page: 1,
    }]);
  });

  it('ignores the forbidden error returned from the graph client', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      page: 1,
      success: false,
      error: { statusCode: 403, message: 'Forbidden' },
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
    }]);

    // act
    const result = searchFromGraphFile(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: [],
      hasNext: false,
    }]);
  });

  it('doesn\'t return hasNext when the page is the last one even if the result exceeds MAX_RESULT', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      page: MAX_PAGE_GRAPH,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(201), (x, i) => `id_${i}`),
    }]);

    // act
    const result = searchFromGraphFile(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      page: MAX_PAGE_GRAPH,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(20), (x, i) => `id_${i}`),
      hasNext: false,
    }]);
  });

  it('returns hasNext when the result exceeds MAX_RESULT', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: { operator: 'And', operand: ['a', 'b'] },
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(201), (x, i) => `id_${i}`),
    }]);

    // act
    const result = searchFromGraphFile(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      page: 1,
      success: true,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(20), (x, i) => `id_${i}`),
      hasNext: true,
    }]);
  });
});
