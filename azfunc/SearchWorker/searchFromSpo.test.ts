import ISearchParameters from "../types/ISearchParameters";
import { ISharePointListsResponse } from "../types/ISharePointListResponse";
import { createCamlBodyWithSearchWords, createListUrlOfCaml } from "../utilities/sharepoint";
import searchFromSpo from "./searchFromSpo";
import { createSpClient } from "../utilities/spClient";
import { Client } from "@microsoft/microsoft-graph-client";
import { executeAll } from "../utilities/api";
import { parseSharePointPath } from "../utilities/url";
import { RequestInit } from "node-fetch";
import { MAX_PAGE_SPO } from "../utilities/lib";

jest.mock('../utilities/api');
jest.mock('../utilities/sharepoint');
jest.mock('../utilities/spClient');
jest.mock('../utilities/url');
jest.mock('@microsoft/microsoft-graph-client');

const fetchListResult = jest.fn<Promise<ISharePointListsResponse>, []>();

const createSpClientMock = createSpClient as jest.MockedFunction<typeof createSpClient>;
const executeAllMock = executeAll as jest.MockedFunction<typeof executeAll>;
const createListUrlOfCamlMock = createListUrlOfCaml as jest.MockedFunction<typeof createListUrlOfCaml>;
const createCamlBodyWithSearchWordsMock = createCamlBodyWithSearchWords as jest.MockedFunction<typeof createCamlBodyWithSearchWords>;
const parseSharePointPathMock = parseSharePointPath as jest.MockedFunction<typeof parseSharePointPath>;

let containedToken: string | undefined;

describe('searchFromSpo', () => {

  beforeEach(() => {
    containedToken = undefined;
    executeAllMock.mockClear();
    fetchListResult.mockClear();
    createSpClientMock.mockClear();
    createSpClientMock.mockReturnValue({} as Client);
    createListUrlOfCamlMock.mockClear();
    createListUrlOfCamlMock.mockImplementation((list: string) => `caml_${list}`);
    createCamlBodyWithSearchWordsMock.mockClear();
    createCamlBodyWithSearchWordsMock.mockImplementation((rowLimit, fields, searchWords) => (
      {
        query: {
          ViewXml: JSON.stringify({rowLimit, fields, searchWords}),
        }
      }
    ));
  });

  describe('the passed getRequest callback', () => {
    it('returns request', async() => {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: {operator: 'And', operand: ['a', 'b']},
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
        },
      };
      createSpClientMock.mockClear();
      createSpClientMock.mockReturnValueOnce({} as Client);
      const theClient = {} as Client;
      createSpClientMock.mockReturnValue(theClient);
      executeAllMock.mockResolvedValue([{
        success: true,
        page: 1,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromSpo(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedRequest = param.getRequest(param.context[0]);

      // assert
      expect(resolvedRequest.id).toBe('');
      expect(resolvedRequest.url).toBe('e1_api/caml_f1');
      expect((resolvedRequest as RequestInit).method).toBe('POST');
      expect((resolvedRequest as RequestInit).headers).toEqual({
        accept: 'application/json;odata=verbose',
      });
      expect((resolvedRequest as RequestInit).body as string).toBe('{"query":{"ViewXml":"{\\"rowLimit\\":201,\\"fields\\":[\\"Title\\",\\"docBody\\",\\"g1\\"],\\"searchWords\\":{\\"operator\\":\\"And\\",\\"operand\\":[\\"a\\",\\"b\\"]}}"}}');
    });
  });

  describe('the passed getIds callback', () => {
    it('returns GUIDs from fetched list', async () =>
    {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: {operator: 'And', operand: ['a', 'b']},
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
        },
      };
      createSpClientMock.mockClear();
      createSpClientMock.mockReturnValueOnce({} as Client);
      const theClient = {} as Client;
      createSpClientMock.mockReturnValue(theClient);
      executeAllMock.mockResolvedValue([{
        success: true,
        page: 1,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromSpo(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedIds = param.getIds({value: [
          {
            GUID: 'Guid1',
            Title: 'Title1',
          },
          {
            GUID: 'Guid2',
            Title: 'Title2',
          },
        ]});

      // assert
      expect(resolvedIds).toEqual(['Guid1', 'Guid2']);
    });

    it ('returns empty array from empty fetched list', async () => {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: {operator: 'And', operand: ['a', 'b']},
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
        },
      };
      createSpClientMock.mockClear();
      createSpClientMock.mockReturnValueOnce({} as Client);
      const theClient = {} as Client;
      createSpClientMock.mockReturnValue(theClient);
      executeAllMock.mockResolvedValue([{
        success: true,
        page: 1,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromSpo(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedIds = param.getIds({value: []});

      // assert
      expect(resolvedIds).toEqual([]);
    });

    it ('returns only items that have Guid', async () => {
      // arrange
      const parameters: ISearchParameters = {
        conditionKeywords: {operator: 'And', operand: ['a', 'b']},
        res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
        properties: {
          global: {'site': 'ga1' },
          local: [{ properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1,}],
        },
      };
      createSpClientMock.mockClear();
      createSpClientMock.mockReturnValueOnce({} as Client);
      const theClient = {} as Client;
      createSpClientMock.mockReturnValue(theClient);
      executeAllMock.mockResolvedValue([{
        success: true,
        page: 1,
        properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
        uniqueKey: 'f1',
        ids: ['Guid1', 'Guid2'],
      }]);

      // act
      await searchFromSpo(parameters);
      // test callback
      const param = executeAllMock.mock.calls[0][0];
      const resolvedIds = param.getIds({value: [
        {
          Title: 'Title1',
        },
        {
          GUID: 'Guid2',
          Title: 'Title2',
        },
      ]});

      // assert
      expect(resolvedIds).toEqual(['Guid2']);
    });
  });

  it('passes the received parameters to the spo client', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: {operator: 'And', operand: ['a', 'b']},
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      success: true,
      page: 1,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: ['Guid1', 'Guid2'],
    }]);

    // act
    const result = searchFromSpo(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      success: true,
      page: 1,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: ['Guid1', 'Guid2'],
      hasNext: false,
    }]);

    const param = executeAllMock.mock.calls[0][0];
    expect(param.process).toBe('graph');
    // expect(param.endPoint).toBe('ga1/_api');
    // expect(param.token).toBe('spoToken');
    expect(param.context).toEqual([{
      properties: {
        site: 'e1',
        list: 'f1',
        category: 'g1',
      },
      page: 1,
    }]);
  });

  it('ignores the forbidden error returned from the spo client', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: {operator: 'And', operand: ['a', 'b']},
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      success: false,
      page: 1,
      error: { statusCode: 403, message: 'Forbidden' },
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
    }]);

    // act
    const result = searchFromSpo(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      success: true,
      page: 1,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: [],
      hasNext: false,
    }]);
  });

  it('returns hasNext when the result exceeds MAX_RESULT', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: {operator: 'And', operand: ['a', 'b']},
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      success: true,
      page: 1,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(201), (x, i) => `id_${i}`),
    }]);

    // act
    const result = searchFromSpo(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      success: true,
      page: 1,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(200), (x, i) => `id_${i}`),
      hasNext: true,
    }]);
  });

  it('doesn\'t return hasNext when page exceeds MAX_PAGE even if the result exceeds MAX_RESULT', async () =>
  {
    // arrange
    const parameters: ISearchParameters = {
      conditionKeywords: {operator: 'And', operand: ['a', 'b']},
      res: {'Spo': 'spoToken', 'Another': 'anotherToken'},
      properties: {
        global: {'site': 'ga1/' },
        local: [{properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'}, page: 1, }],
      },
    };
    executeAllMock.mockResolvedValue([{
      success: true,
      page: MAX_PAGE_SPO,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(201), (x, i) => `id_${i}`),
    }]);

    // act
    const result = searchFromSpo(parameters);

    // assert
    await expect(result).resolves.toEqual([{
      success: true,
      page: MAX_PAGE_SPO,
      properties: {'site': 'e1', 'list': 'f1', 'category': 'g1'},
      uniqueKey: 'f1',
      ids: Array.from(new Array(200), (x, i) => `id_${i}`),
      hasNext: false,
    }]);
  });});