/*
 * TableStorageへの薄いラッパー
 * 型パラメータ付きのメンバーを返すインスタンスのモックが難しいため、
 * テストを容易にするために導入
 */

import { TableClient } from "@azure/data-tables";
/**
 * a table name of search entities
 */
const SEARCH_TABLE = 'search';

export type ITableMessage = {
  partitionKey: string,
  rowKey: string,
  ConditionKeywords?: string,
  ReqId?: { type: string, value: string },
  State?: string,
  Tokens?: string,
  UserId?: string,
  Pid?: string,
  Kind?: string,
  Properties?: string,
  Ids?: string,
  HasNext?: boolean,
};

export type ITableOperations = {
  get: (partitionKey: string, rowKey: string) => Promise<ITableMessage>,
  create: (message: ITableMessage) => Promise<void>,
  update: (message: ITableMessage) => Promise<void>,

};

export function getTable(connectionString: string): ITableOperations {
  const tableClient = TableClient.fromConnectionString(connectionString, SEARCH_TABLE);

  return {
    get: (partitionKey: string, rowKey: string) => tableClient.getEntity<ITableMessage>(partitionKey, rowKey),
    create: async (message: ITableMessage) => {tableClient.createEntity(message);},
    update: async (message: ITableMessage) => {tableClient.updateEntity(message, 'Merge');},
  };
};

