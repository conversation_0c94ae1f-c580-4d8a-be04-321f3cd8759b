import { AzureFunction, Context } from "@azure/functions"
import { TimerObject } from '../utilities/models';
import {
  validateCosmosDBConnection,
  deleteOldMessagesFromCosmosDB
} from '../utilities/cosmos';

/**
 * タイマー起動間隔はfunction.jsonにCRON式またはTimeSpan値で記載する
 * https://docs.microsoft.com/ja-jp/azure/azure-functions/functions-bindings-timer?tabs=csharp#ncrontab-expressions
 * https://crontab-generator.org/
 * @param context
 * @param timerObj
 */
const main: AzureFunction = async function (context: Context, timerObj: TimerObject): Promise<void> {
  const logger = context.log;

  logger('========== PHASE 1 ==========');

  // DB接続に問題がないか確認
  try {
    await validateCosmosDBConnection(logger);
    logger("Successfully Connected to CosmosDB.");
  } catch (error) {
    logger("CosmosDB Connection Failed:", error);
    throw error;
  }

  logger('========== PHASE 2 ==========');

  const filterDate = new Date(new Date().setFullYear(new Date().getFullYear() - 1));
  filterDate.setDate(filterDate.getDate() - 1);
  const formattedFilterDate = filterDate.toISOString().split('T')[0];
  logger("formattedFilterDate:", formattedFilterDate);

  try {
    await deleteOldMessagesFromCosmosDB(logger, formattedFilterDate);
    logger("Successfully Deleted Old Messages.");
  } catch (error) {
    logger("CosmosDB Deleting Failed:", error);
    throw error;
  }
};

export default main;
