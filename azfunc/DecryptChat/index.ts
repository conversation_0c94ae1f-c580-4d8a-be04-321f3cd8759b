import { AzureFunction, Context, HttpRequest } from "@azure/functions";
import CryptoJS from 'crypto-js';

const AES_SECRET_KEY = process.env['AES_SECRET_KEY']!;
const IV = CryptoJS.enc.Hex.parse(process.env['AES_IV_KEY']!);
const BATCH_SIZE = parseInt(process.env['DECRYPT_BATCH_SIZE'] ?? '100');
const MAX_PROCESSING_TIME = parseInt(process.env['MAX_PROCESSING_TIME'] ?? '240000');

const PARSED_KEY = CryptoJS.enc.Utf8.parse(AES_SECRET_KEY);

function decrypt(encryptedMessage: string): string {
  try {
    const decrypted = CryptoJS.AES.decrypt(
      CryptoJS.lib.CipherParams.create({
        ciphertext: CryptoJS.enc.Base64.parse(encryptedMessage)
      }),
      PARSED_KEY,
      {
        iv: IV,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      }
    );
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    throw new Error('Decryption Failed: ' + (error instanceof Error ? error.message : 'Unknown Error'));
  }
}

async function processBatch(batch: any[], logger: any, batchIndex: number) {
  const startTime = Date.now();
  
  const results = await Promise.allSettled(batch.map(async (value, itemIndex) => {
    try {
      const requestData = value.data;
      // logger.info(`value: ${JSON.stringify(value)}`);
      // logger.info(`requestData: ${JSON.stringify(requestData)}`);
      const id = value.recordId;

      if (!requestData?.id) {
        return {
          recordId: id,
          id: requestData?.id ?? null,
          success: false,
          data: null,
          errors: [{ message: "Data or ID is missing" }]
        };
      }

      const decryptedData = {
        id: requestData.id,
        d_replyToId: requestData.replyToId ? decrypt(requestData.replyToId) : null,
        d_messageType: requestData.messageType ? decrypt(requestData.messageType) : null,
        d_subject: requestData.subject ? decrypt(requestData.subject) : null,
        d_from: requestData.from ? {
          application: requestData.from.application ? {
            displayName: decrypt(requestData.from.application.displayName)
          } : null,
          device: requestData.from.device ? {
              displayName: decrypt(requestData.from.device.displayName)
          } : null,
          user: requestData.from.user ? {
              id: decrypt(requestData.from.user.id),
              displayName: decrypt(requestData.from.user.displayName)
          } : null
        } : null,
        d_body: {
          content: requestData.body?.content ? decrypt(requestData.body?.content) : null,
        },
        d_channelIdentity: requestData.channelIdentity ? {
          teamId: requestData.channelIdentity.teamId ?? null,
          channelId: requestData.channelIdentity.channelId ? decrypt(requestData.channelIdentity.channelId) : null
        } : null
      };

      return {
        recordId: id,
        id: requestData.id,
        success: true,
        data: decryptedData,
        errors: null
      };
    } catch (error) {
      const recordId = value.recordId;
      const dataId = value.data?.id ?? 'unknown';

      // Reduced logging for large datasets to improve performance
      if (batchIndex % 100 === 0) {
        logger.error(`Error processing record in batch ${batchIndex}, item ${itemIndex}:`, {
          recordId,
          id: dataId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }

      return {
        recordId,
        id: dataId,
        success: false,
        data: null,
        errors: [{ message: error instanceof Error ? error.message : 'An unexpected error occurred' }]
      };
    }
  }));

  const processingTime = Date.now() - startTime;

  if (batchIndex % 50 === 0) {
    logger.info(`Batch ${batchIndex}: Processed ${batch.length} items in ${processingTime}ms`);
  }

  return results.map((result, index) => {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      const recordId = batch[index]?.recordId ?? `batch-${batchIndex}-item-${index}`;
      const dataId = batch[index]?.data?.id ?? 'unknown';
      return {
        recordId,
        id: dataId,
        success: false,
        data: null,
        errors: [{ message: 'Processing Failed' }]
      };
    }
  });
}

const main: AzureFunction = async function (context: Context, req: HttpRequest): Promise<void> {
  const logger = context.log;
  const startTime = Date.now();
  
  try {
    // Validation
    if (!AES_SECRET_KEY) {
      context.res = {
        status: 500,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ values: [], error: "Configuration error" })
      };
      return;
    }

    if (!req.body?.values || !Array.isArray(req.body.values) || req.body.values.length === 0) {
      context.res = {
        status: 400,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ values: [], error: "Invalid request" })
      };
      return;
    }

    const totalItems = req.body.values.length;
    
    logger.info(`Processing ${totalItems} items with batch size ${BATCH_SIZE}`);

    const decryptedValues = [];
    const batches = [];

    for (let i = 0; i < totalItems; i += BATCH_SIZE) {
      batches.push(req.body.values.slice(i, i + BATCH_SIZE));
    }

    for (let i = 0; i < batches.length; i++) {
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime > MAX_PROCESSING_TIME) {
        logger.warn(`Approaching timeout at batch ${i}/${batches.length}. Processed ${i * BATCH_SIZE} items.`);
        break;
      }

      const batch = batches[i];
      const batchResults = await processBatch(batch, logger, i);
      decryptedValues.push(...batchResults);
      
      // Memory management
      batches[i] = [];
      
      if (global.gc && i % 20 === 0) {
        global.gc();
      }
    }

    batches.length = 0;

    const totalTime = Date.now() - startTime;
    const successCount = decryptedValues.filter(r => r.success).length;
    const errorCount = decryptedValues.filter(r => !r.success).length;

    logger.info(`Completed: ${successCount} success, ${errorCount} errors in ${totalTime}ms`);

    context.res = {
      status: 200,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ 
        values: decryptedValues,
        _metadata: {
          processed: decryptedValues.length,
          success: successCount,
          errors: errorCount,
          processingTimeMs: totalTime
        }
      })
    };

  } catch (error) {
    logger.error('Critical error in decrypt function:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      requestSize: req.body?.values?.length ?? 0
    });
    
    context.res = {
      status: 500,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        values: [],
        error: error instanceof Error ? error.message : 'An unexpected error occurred'
      })
    };
  }
};

export default main;