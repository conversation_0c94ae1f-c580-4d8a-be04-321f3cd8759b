const MESSAGE_SEARCH_SIZE: string = process.env['MESSAGE_SEARCH_SIZE'] ?? '20';
const MESSAGE_START_DATE: string = process.env['MESSAGE_START_DATE'] ?? '2025-01-01';
const MESSAGE_END_DATE: string = process.env['MESSAGE_END_DATE'] ?? '2025-12-31';

interface UserMessageRequest {
  id: string;
  method: string;
  url: string;
}

/**
 * ユーザーごとのメールデータを取得するクエリを作成
 * @param userId
 */
function createCalendarWeekChunks(startDate: Date, endDate: Date): Array<{ start: string; end: string }> {
  const chunks = [];
  let currentDate = new Date(startDate);
  
  // Determine the day of week for the start date (0 = Sunday, 1 = Monday, etc.)
  const dayOfWeek = currentDate.getDay();
  
  if (dayOfWeek !== 0) {
    // Create a partial week from start date to the next Saturday
    const daysUntilSaturday = 6 - dayOfWeek; // Days until Saturday (6 - dayOfWeek)
    const nextSaturday = new Date(currentDate);
    nextSaturday.setDate(currentDate.getDate() + daysUntilSaturday);
    
    // If the first Saturday is beyond our end date, adjust accordingly
    const firstChunkEnd = nextSaturday > endDate ? endDate : nextSaturday;
    
    chunks.push({
      start: currentDate.toISOString().split('T')[0],
      end: firstChunkEnd.toISOString().split('T')[0]
    });
    
    // Set current date to the next Sunday
    currentDate = new Date(nextSaturday);
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  // Create full week chunks (Sunday to Saturday)
  while (currentDate < endDate) {
    // End date is Saturday (6 days after Sunday)
    const endOfWeek = new Date(currentDate);
    endOfWeek.setDate(currentDate.getDate() + 6);
    
    // If end of week exceeds the overall end date, use the end date
    const chunkEndDate = endOfWeek > endDate ? endDate : endOfWeek;
    
    chunks.push({
      start: currentDate.toISOString().split('T')[0],
      end: chunkEndDate.toISOString().split('T')[0]
    });
    
    // Move to next Sunday
    currentDate = new Date(endOfWeek);
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return chunks;
}

export function createUserMessageRequests(userId: string): UserMessageRequest[] {
  if (!userId) return [];

  const startDate = new Date(MESSAGE_START_DATE);
  const endDate = new Date(MESSAGE_END_DATE);
  const requests: UserMessageRequest[] = [];

  const dateChunks = createCalendarWeekChunks(startDate, endDate);
  
  // Create batch requests for each chunk
  dateChunks.forEach((chunk, index) => {
    requests.push({
      id: `${userId}-week${index + 1}`,
      method: 'GET',
      url: `/users/${userId}/messages?$top=${MESSAGE_SEARCH_SIZE}&$filter=receivedDateTime ge ${chunk.start} and receivedDateTime le ${chunk.end}&$orderby=receivedDateTime desc`
    });
  });
  
  return requests;
}

function formatDate(date: Date): string {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}

function createYesterdayTodayChunks(): Array<{ start: string; end: string }> {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const yesterdayStart = `${formatDate(yesterday)}T00:00:00Z`;
  const todayEnd = `${formatDate(today)}T23:59:59Z`;

  return [{
    start: yesterdayStart,
    end: todayEnd
  }];
}

export function createUserMessageRequestsDaily(userId: string): UserMessageRequest[] {
  if (!userId) return [];

  const dateChunks = createYesterdayTodayChunks();

  return [{
    id: `${userId}`,
    method: 'GET',
    url: `/users/${userId}/messages?$top=${MESSAGE_SEARCH_SIZE}&$filter=receivedDateTime ge ${dateChunks[0].start} and receivedDateTime le ${dateChunks[0].end}&$orderby=receivedDateTime desc`
  }];
}