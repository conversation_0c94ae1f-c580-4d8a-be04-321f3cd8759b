import { Container, CosmosClient } from "@azure/cosmos";
import { Context } from '@azure/functions';
import {
  IBatchResponseData,
  IMessageData
} from '../utilities/models';

const cosmosDBEndpoint = process.env['COSMOS_DB_ENDPOINT'];
const cosmosDBKey = process.env['COSMOS_DB_KEY'];
const databaseName = process.env['COSMOS_DB_DATABASE'];
const containerName = process.env['COSMOS_DB_CONTAINER'];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: Context['log']) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:Client] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger('[CosmosDB:Client] Initializing Client Connection...');
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDBKey
      });
      logger('[CosmosDB:Client] Client Initialized Successfully');
    } else {
      logger('[CosmosDB:Client] Reusing Existing Connection');
    }

    return cosmosClient;

  } catch (error) {
    logger(`[CosmosDB:Client] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: Context['log'],
): Promise<void> {

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Validate] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger(`[CosmosDB:Validate] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases.readAll().fetchAll();
    logger(`[CosmosDB:Validate] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger(`[CosmosDB:Validate] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:Validate] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger(`[CosmosDB:Validate] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:Validate] Container resource is undefined");
    }

    // Count Container Details
    const { resources: [count] } = await container.items
      .query("SELECT VALUE COUNT(1) FROM c")
      .fetchAll();
    logger(`[CosmosDB:Validate] Container has: ${count} Items`);

  } catch (error) {
    logger(`[CosmosDB:Validate] Error Connection: ${error}`);
    throw error;
  }
}

export async function insertMessagesToCosmosDB(
  logger: Context['log'],
  userMessages: IBatchResponseData[]
): Promise<void> {

  // logger(`[CosmosDB:Insert] userMessages`, userMessages);
  
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Insert] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    // Extract the body.value
    const modifiedMessages: IMessageData[][] = userMessages
      .map(message => message.body?.value || [])
      .filter(Array.isArray);
    const insertedCount = await processMessages(container, modifiedMessages, logger);

    logger(`[CosmosDB:Insert] Inserted ${insertedCount} New Messages to Cosmos DB`);

    const totalMessageCount = modifiedMessages.reduce((count, array) => count + (array ? array.length : 0), 0);
    logger(`[CosmosDB:Insert] Skipped ${totalMessageCount - insertedCount} Existing Messages`);

  } catch (error) {
    logger(`[CosmosDB:Insert] Error Messages: ${error}`);
    throw error;
  }
}

async function processMessages(
  container: Container,
  modifiedMessages: IMessageData[][],
  logger: Context['log']
): Promise<number> {
  let insertedCount = 0;

  // logger(`[CosmosDB:Insert] modifiedMessages`, modifiedMessages);
  
  for (const messageArray of modifiedMessages) {
    for (const message of messageArray) {

      if (!message.id) {
        logger(`[CosmosDB:Process] Error: message.id is undefined for message: ${JSON.stringify(message.id)}`);
        continue;
      }

      const querySpec = {
        query: "SELECT * FROM c WHERE c.id = @id",
        parameters: [{ name: "@id", value: message.id }]
      };

      try {
        const { resources: existingMessages } = await container.items.query(querySpec).fetchAll();
        if (existingMessages.length === 0) {
          await container.items.create(message);
          insertedCount++;
        }
      } catch (error) {
        logger(`[CosmosDB:Process] Error processing message: ${error}`);
      }
    }
  }

  return insertedCount;
}

export async function deleteOldMessagesFromCosmosDB(
  logger: Context['log'],
  filterDate: string
): Promise<void> {

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Delete] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const encodedDate = encodeURIComponent(filterDate);
    logger("[CosmosDB:Delete] Date:", encodedDate);

    const container = client.database(databaseName).container(containerName);

    const querySpec = {
      query: "SELECT * FROM c WHERE c.receivedDateTime <= @filterDate ORDER BY c.receivedDateTime DESC",
      parameters: [{ name: "@filterDate", value: filterDate }]
    };

    const { resources: oldMessages } = await container.items.query(querySpec).fetchAll();
    logger(`Found ${oldMessages.length} Messages to Delete`);

    oldMessages.forEach(message => {
      // logger(`[CosmosDB:Delete] Message-ID to delete: ${message.id}`);
      logger(`[CosmosDB:Delete] Message receivedDateTime: ${message.receivedDateTime}`);
    });

    // const containerDefinition = await container.read();
    // logger("Partition Key Path:", containerDefinition.resource?.partitionKey?.paths[0]);

    let deletedCount = 0;

    for (const message of oldMessages) {
      try {
        await container.item(message.id, message.id).delete();
        deletedCount++;
        logger(`[CosmosDB:Delete] Successfully Deleted Message-ID: ${message.id}`);
      } catch (error) {
        logger(`[CosmosDB:Delete] Error Deleting Message-ID: ${message.id}: ${error}`);
      }
    }

    logger(`[CosmosDB:Delete] Deleted ${deletedCount} Messages from Cosmos DB`);

    const { resources: [count] } = await container.items
      .query("SELECT VALUE COUNT(1) FROM c")
      .fetchAll();
    logger(`[CosmosDB:Delete] Container has: ${count} Items`);

  } catch (error) {
    logger(`[CosmosDB:Delete] Error Messages: ${error}`);
    throw error;
  }
}

export async function deleteAllDocumentsFromCosmosDB(logger: Context['log']): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Delete] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const database = client.database(databaseName);
    const container = database.container(containerName);

    logger(`[CosmosDB:Delete] Deleting Entire Container: ${containerName}`);

    await container.delete();
    await database.containers.createIfNotExists({ id: containerName });

    logger(`[CosmosDB:Delete] All Data Deleted. Container Recreated.`);
  } catch (error) {
    logger(`[CosmosDB:Delete] Error: ${error}`);
    throw error;
  }
}