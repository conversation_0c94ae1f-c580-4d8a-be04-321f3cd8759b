
import { AzureFunction, Context } from "@azure/functions";
import { DefaultAzureCredential } from "@azure/identity";
import { WebSiteManagementClient } from "@azure/arm-appservice";
import { BlobServiceClient } from "@azure/storage-blob";
import { SearchIndexerClient, AzureKeyCredential } from "@azure/search-documents";

const requiredEnvVars = [
  "AzureSubscriptionId", "ResourceGroupName", "AppServiceName", "SearchServiceName", "SearchApiKey", "ApiVersion",
  "StorageAccount",
  "IndexA", "IndexB",
  "SemanticConfigA", "SemanticConfigB",
  "IndexerA", "IndexerB",
  "IndexABlobName", "IndexBBlobName",
  "Env", "Company", "APPLICATIONINSIGHTS_CONNECTION_STRING"
];


const main: AzureFunction = async function(context: Context, timerObj: any): Promise<void> {
  const logger = context.log;
  logger("========== PHASE 1 ==========");

  // 環境変数取得
  const envValues: { [key: string]: string } = {};
  for (const key of requiredEnvVars) {
    envValues[key] = process.env[key] || "";
  }
  envValues["ContainerName"] = "sources";

  // 必須パラメータチェック
  const missingVars = Object.keys(envValues).filter(k => !envValues[k]);
  if (missingVars.length > 0) {
    logger(`[ERROR] 必要な環境変数が不足しています: ${missingVars.join(", ")}`);
    return Promise.reject("NO_ENV_VARS");
  }

  // AppServiceのApplication Settings全体取得
  let settings: { [key: string]: string } = {};
  try {
    const subscriptionId = envValues["AzureSubscriptionId"];
    const resourceGroupName = envValues["ResourceGroupName"];
    const appServiceName = envValues["AppServiceName"];
    const credential = new DefaultAzureCredential();
    const client = new WebSiteManagementClient(credential, subscriptionId);
    const appSettingsResult = await client.webApps.listApplicationSettings(resourceGroupName, appServiceName);
    settings = appSettingsResult.properties || {};
  } catch (e: any) {
    logger(`[ERROR] Application Settingsの取得に失敗しました: ${e?.message || e}`);
    return Promise.reject("APP_SERVICE_SETTINGS_ERROR");
  }

  // 現状値の取得と次に設定する値の判定
  const currentIndex = settings["AISearch:SPO:0:SPOIndex"] || "";
  const currentSemantic = settings["AISearch:SPO:0:SPOSemanticConfiguration"] || "";
  let nextIndex = "";
  let nextSemantic = "";
  let nextIndexer = "";
  let blobName = "";
  if (currentIndex === envValues["IndexA"]) {
    nextIndex = envValues["IndexB"];
    nextSemantic = envValues["SemanticConfigB"];
    nextIndexer = envValues["IndexerB"];
    blobName = envValues["IndexABlobName"];
  } else if (currentIndex === envValues["IndexB"]) {
    nextIndex = envValues["IndexA"];
    nextSemantic = envValues["SemanticConfigA"];
    nextIndexer = envValues["IndexerA"];
    blobName = envValues["IndexBBlobName"];
  } else {
    logger(`[ERROR] currentIndexがIndexA/IndexBどちらにも一致しません: ${currentIndex}`);
    return Promise.reject("INVALID_CURRENT_INDEX");
  }

  // インデクサー操作用のエンドポイント作成
  const searchEndpoint = `https://${envValues["SearchServiceName"]}.search.windows.net`;

    logger("========== PHASE 2 ==========");

  // インデクサー状態確認
  try {
    const client = new SearchIndexerClient(
      searchEndpoint,
      new AzureKeyCredential(envValues["SearchApiKey"])
    );
    const status = await client.getIndexerStatus(nextIndexer);
    if (!status.lastResult || status.lastResult.status !== "success") {
      logger(`[ERROR] インデクサー ${nextIndexer} の最新実行が成功していません。Status: ${status.lastResult?.status}`);
      return Promise.reject("INDEXER_STATUS_ERROR");
    }
  } catch (e: any) {
    logger(`[ERROR] インデクサー状態の取得に失敗しました: ${e?.message || e}`);
    return Promise.reject("INDEXER_STATUS_ERROR");
  }

  // AppSettingsのインデックス情報のみを切り替えます
  try {
    const subscriptionId = envValues["AzureSubscriptionId"];
    const resourceGroupName = envValues["ResourceGroupName"];
    const appServiceName = envValues["AppServiceName"];
    const credential = new DefaultAzureCredential();
    const client = new WebSiteManagementClient(credential, subscriptionId);
    // 既存AppSettings取得
    const appSettingsResult = await client.webApps.listApplicationSettings(resourceGroupName, appServiceName);
    const settings = appSettingsResult.properties || {};
    // 2つの値のみ更新
    const updatedSettings = { ...settings };
    updatedSettings["AISearch:SPO:0:SPOIndex"] = nextIndex;
    updatedSettings["AISearch:SPO:0:SPOSemanticConfiguration"] = nextSemantic;
    await client.webApps.updateApplicationSettings(resourceGroupName, appServiceName, { properties: updatedSettings });
  } catch (e: any) {
    logger(`[ERROR] インデックス情報の切替に失敗しました: ${e?.message || e}`);
    return Promise.reject("UPDATE_APP_SETTINGS_ERROR");
  }

    logger("========== PHASE 3 ==========");

  // Blob取得
  let indexDefinitionJson = "";
  try {
    const blobServiceClient = new BlobServiceClient(
      `https://${envValues["StorageAccount"]}.blob.core.windows.net`,
      new DefaultAzureCredential()
    );
    const containerClient = blobServiceClient.getContainerClient(envValues["ContainerName"]);
    const blobClient = containerClient.getBlobClient(blobName);
    const downloadBlockBlobResponse = await blobClient.download();
    const downloaded = await streamToString(downloadBlockBlobResponse.readableStreamBody ?? null);
    // 可変項目置換
    let indexJsonRawStr = downloaded.replace(/\{\{Env\}\}/g, envValues["Env"]);
    indexJsonRawStr = indexJsonRawStr.replace(/\{\{Company\}\}/g, envValues["Company"]);
    indexDefinitionJson = indexJsonRawStr;
  } catch (e) {
    logger(`[ERROR] Blob「${blobName}」の取得に失敗しました: ${e}`);
    return Promise.reject("BLOB_ERROR");
  }

  // 古いインデックス削除
  try {
    const deleteUri = `${searchEndpoint}/indexes/${currentIndex}?api-version=${envValues["ApiVersion"]}`;
    const deleteRes = await fetch(deleteUri, {
      method: "DELETE",
      headers: { "api-key": envValues["SearchApiKey"] }
    });
    if (!deleteRes.ok) throw new Error("インデックス削除失敗");
  } catch (e) {
    logger(`[ERROR] インデックス「${currentIndex}」の削除に失敗しました: ${e}`);
    return Promise.reject("DELETE_INDEX_ERROR");
  }

  // インデックス再作成
  try {
    const createUri = `${searchEndpoint}/indexes/${currentIndex}?api-version=${envValues["ApiVersion"]}`;
    const createRes = await fetch(createUri, {
      method: "PUT",
      headers: {
        "api-key": envValues["SearchApiKey"],
        "Content-Type": "application/json"
      },
      body: indexDefinitionJson
    });
    if (!createRes.ok) {
      const errorText = await createRes.text();
      logger(`[ERROR] インデックス「${currentIndex}」の再作成APIレスポンス: ${errorText}`);
      throw new Error("インデックス再作成失敗");
    }
  } catch (e) {
    logger(`[ERROR] インデックス「${currentIndex}」の再作成に失敗しました: ${e}`);
    return Promise.reject("CREATE_INDEX_ERROR");
  }

  logger(`[SUCCESS] インデックス切替処理が完了しました: ${currentIndex} → ${nextIndex}`);
};

// ストリーム→文字列変換ヘルパー
async function streamToString(readableStream: NodeJS.ReadableStream | null): Promise<string> {
  if (!readableStream) return "";
  return new Promise((resolve, reject) => {
    const chunks: any[] = [];
    readableStream.on("data", (data) => {
      chunks.push(data.toString());
    });
    readableStream.on("end", () => {
      resolve(chunks.join(""));
    });
    readableStream.on("error", reject);
  });
}

export default main;
