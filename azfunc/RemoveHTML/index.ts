import { AzureFunction, Context, HttpRequest } from "@azure/functions";
import * as cheerio from "cheerio";

const BATCH_SIZE = parseInt(process.env["HTML_BATCH_SIZE"] ?? "100");
const MAX_PROCESSING_TIME = parseInt(process.env["MAX_PROCESSING_TIME"] ?? "240000");

function removeHtml(htmlText: string): string {
  try {
    if (!htmlText || typeof htmlText !== 'string') {
      return '';
    }

    // Pre-clean
    let preCleanedHtml = htmlText
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '');

    const $html = cheerio.load(preCleanedHtml);
    
    // Remove HTML Elements
    $html('script').remove();
    $html('style').remove();
    $html('noscript').remove();
    $html('meta').remove();
    $html('title').remove();
    $html('head').remove();
    $html('link').remove();
    $html('svg').remove();
    $html('img').remove();
    $html('code').remove();
    $html('pre').remove();
    $html('details').remove();
    
    $html('*').contents().filter(function() {
      return this.nodeType === 8;
    }).remove();
    
    $html('div, p, h1, h2, h3, h4, h5, h6, li, td, th, br').before(' ');
    
    // Get clean text
    let cleanText = $html.text();
    
    // Clean up whitespace
    cleanText = cleanText
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .replace(/\\"/g, '"')
      .replace(/\\n/g, ' ')
      .replace(/\\/g, '')
      .trim();
    
    return cleanText;
  } catch (error) {
    throw new Error('HTML removal failed: ' + (error instanceof Error ? error.message : 'Unknown Error'));
  }
}

async function processBatch(batch: any[], logger: any, batchIndex: number) {
  const startTime = Date.now();

  const results = await Promise.allSettled(
    batch.map(async (value, itemIndex) => {
      try {
        const requestData = value.data;
        const id = value.recordId;

        if (!requestData?.body) {
          return {
            recordId: id,
            id: requestData?.id ?? null,
            success: true,
            data: {
              id: requestData?.id ?? null,
              clean_body: '',
            },
            errors: null,
          };
        }

        const cleanBody = removeHtml(requestData.body);

        return {
          recordId: id,
          id: requestData.id ?? null,
          success: true,
          data: {
            id: requestData.id ?? null,
            clean_body: cleanBody,
          },
          errors: null,
        };
      } catch (error) {
        const recordId = value.recordId;
        const dataId = value.data?.id ?? "unknown";

        // Reduced logging for large datasets to improve performance
        if (batchIndex % 100 === 0) {
          logger.error( `Error processing record in batch ${batchIndex}, item ${itemIndex}:`,
            {
              recordId,
              id: dataId,
              error: error instanceof Error ? error.message : "Unknown error",
            }
          );
        }

        return {
          recordId,
          id: dataId,
          success: false,
          data: null,
          errors: [
            {
              message: error instanceof Error ? error.message : "An unexpected error occurred",
            },
          ],
        };
      }
    })
  );

  const processingTime = Date.now() - startTime;

  if (batchIndex % 50 === 0) {
    logger.info(`Batch ${batchIndex}: Processed ${batch.length} items in ${processingTime}ms`);
  }

  return results.map((result, index) => {
    if (result.status === "fulfilled") {
      return result.value;
    } else {
      const recordId = batch[index]?.recordId ?? `batch-${batchIndex}-item-${index}`;
      const dataId = batch[index]?.data?.id ?? "unknown";
      return {
        recordId,
        id: dataId,
        success: false,
        data: null,
        errors: [{ message: "Processing failed" }],
      };
    }
  });
}

const main: AzureFunction = async function (
  context: Context,
  req: HttpRequest
): Promise<void> {
  const logger = context.log;
  const startTime = Date.now();

  try {
    // Validation
    if (!req.body?.values || !Array.isArray(req.body.values) || req.body.values.length === 0
    ) {
      context.res = {
        status: 400,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ values: [], error: "Invalid request" }),
      };
      return;
    }

    const totalItems = req.body.values.length;

    logger.info(`Processing ${totalItems} items with batch size ${BATCH_SIZE}`);

    const processedValues = [];
    const batches = [];

    for (let i = 0; i < totalItems; i += BATCH_SIZE) {
      batches.push(req.body.values.slice(i, i + BATCH_SIZE));
    }

    for (let i = 0; i < batches.length; i++) {
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime > MAX_PROCESSING_TIME) {
        logger.warn(`Approaching timeout at batch ${i}/${batches.length}. Processed ${i * BATCH_SIZE} items.`);
        break;
      }

      const batch = batches[i];
      const batchResults = await processBatch(batch, logger, i);
      processedValues.push(...batchResults);

      batches[i] = [];

      if (global.gc && i % 20 === 0) {
        global.gc();
      }
    }

    batches.length = 0;

    const totalTime = Date.now() - startTime;
    const successCount = processedValues.filter((r) => r.success).length;
    const errorCount = processedValues.filter((r) => !r.success).length;

    logger.info(`Completed: ${successCount} success, ${errorCount} errors in ${totalTime}ms`);

    context.res = {
      status: 200,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        values: processedValues,
        _metadata: {
          processed: processedValues.length,
          success: successCount,
          errors: errorCount,
          processingTimeMs: totalTime,
        },
      }),
    };
  } catch (error) {
    logger.error("Critical error in HTML removal function:", {
      error: error instanceof Error ? error.message : "Unknown error",
      requestSize: req.body?.values?.length ?? 0,
    });

    context.res = {
      status: 500,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        values: [],
        error: error instanceof Error ? error.message : "An unexpected error occurred",
      }),
    };
  }
};

export default main;
