import { AzureFunction, Context } from '@azure/functions';
import { createGraphClient } from '../utilities/graph';
import { logLongArray } from '../utilities/log';
import { IBatchResponseData, TimerObject } from '../utilities/models';
import {
  createCredential,
  fetchTargetUsersByGroupIds,
  sendActivitiesToUsers,
} from './impl';
import { defaultFeedTitleMessage } from '../utilities/feedTitle';

/**
 * main関数 タイマー起動間隔はfunction.jsonにCRON式またはTimeSpan値で記載する
 * https://docs.microsoft.com/ja-jp/azure/azure-functions/functions-bindings-timer?tabs=csharp#ncrontab-expressions
 * https://crontab-generator.org/
 * @param context
 * @param timerObj
 */
const main: AzureFunction = async function(context: Context, timerObj: TimerObject): Promise<void> {
  const logger = context.log;

  logger('========== PHASE 1 ==========');

  // 資格情報を作成
  const credential = createCredential(
    process.env['AzureTenantId'] ?? '',
    process.env['AzureClientId'] ?? '',
    process.env['AzureClientSecret'] ?? ''
  );
  if (!credential) {
    // 資格情報が不足している場合は異常終了
    logger.error('NO_CREDENTIALS');
    return Promise.reject('NO_CREDENTIALS');
  }

  // GraphAPIのクライアントを生成
  const graphClient = createGraphClient(credential);

  logger('========== PHASE 2 ==========');

  // 検索するteamsAppId
  const teamsAppId = process.env['TeamsAppId'] ?? '';
  if (!teamsAppId) {
    // 存在しない場合は異常終了
    logger.error('NO_TEAMS_APP_ID');
    return Promise.reject('NO_TEAMS_APP_ID');
  }

    const  getGroupIds=(()=>{
      if (process.env['GraphGroupIds'] != undefined) {
        return (process.env['GraphGroupIds'])?.split(';')
    }else if (process.env['GraphGroupId'] != undefined){
        return [(process.env['GraphGroupId'])];
    }
    return[]
    })
    const groupIds = getGroupIds()
    if (groupIds.length === 0){
      // 存在しない場合は異常終了
      logger.error('NO_TEAMS_APP_ID');
      return Promise.reject('NO_TEAMS_APP_ID');
    }
    // 通知先となるユーザーを取得する
    const targetUsers = await fetchTargetUsersByGroupIds(logger, graphClient, groupIds, teamsAppId)
      .catch((reason) => {
        logger.error(reason);
        return Promise.reject(reason);
      });

  // 通知できるユーザーがいないので正常終了
  if (targetUsers.length === 0) {
    logger('PROCESS_END_BECAUSE_OF_NO_USERS_TO_NOTIFY');
    return Promise.resolve();
  }

  // output log of the user IDs for each 800 entries
  logLongArray(logger, 'Selected users', 800, targetUsers.map((u) => u.id));

  logger('========== PHASE 3 ==========');

  // 通知を送るためのリクエストデータを生成する
  const feedTitleMessage = process.env['FeedTitleMessage'] || defaultFeedTitleMessage;
  await sendActivitiesToUsers(logger, graphClient, targetUsers, feedTitleMessage)
    // Promise.allなので、全部成功か全部失敗にしかならない
    .catch((reason) => {
      logger.error(reason);
      return [] as IBatchResponseData[];
    })
    .then((results) => {
      results.forEach((data) => {
        // 成功時は204。204以外の結果があればロギング
        if (data.status !== 204) {
          if(data?.body?.error?.innerError) {
            logger.warn(`${data}, ${JSON.stringify(data)}`);
          }
          else{
            logger.warn(data)
          }
        }
      });
    });
};

export default main;
