import "isomorphic-fetch";
import { TokenCredential } from '@azure/core-auth';
import { Context } from "@azure/functions";
import { ClientSecretCredential } from '@azure/identity';
import { BatchRequestData, Client } from '@microsoft/microsoft-graph-client';
import {
  createSendActivityNotificationUrl,
  decorateFeedOptionsAsCompany, decorateFeedOptionsWithChainId, decorateFeedOptionsWithEntityUrl,
} from '../utilities/activityNotifications';
import { splitArrayIntoChunks } from '../utilities/array';
import {
  createInstalledAppsUrlWithAppIdFilter,
  fetchGroupUsers,
  fetchJsonBatch
} from '../utilities/graph';
import { logLongArray } from '../utilities/log';
import {
  IBatchResponseData,
  IBatchResponses, ITeamsActivityNotificationConfig,
} from '../utilities/models';
import { User } from "@microsoft/microsoft-graph-types";

// GraphAPIのバッチリクエスト最大数
const MAX_GRAPH_API_BATCH_COUNTS = 20;

// 上書きするアクティビティフィードのID
const CHAIN_ID = 1;

type Logger = Context['log'];


/**
 * credentialを作成
 * 必須パラメータが存在しない場合はundefinedを返却
 */
export function createCredential(
  tenantId: string,
  clientId: string,
  clientSecret: string,
): TokenCredential | undefined {
  // 必須パラメータが存在しない場合はundefined
  if (!tenantId || !clientId || !clientSecret) {
    return undefined;
  }
  return new ClientSecretCredential(tenantId, clientId, clientSecret);
}

/**
 * グループ内に所属する全ユーザーのうち、カスタムアプリを所持するユーザーだけを抽出する
 * @param logger
 * @param client
 * @param groupIds
 * @param teamsAppId
 */
export async function fetchTargetUsersByGroupIds(
  logger: Logger,
  client: Client,
  groupIds: string[],
  teamsAppId: string,
): Promise<IBatchResponseData[]> {

  // GroupIDに所属するユーザーを取得する
  const users: User[] = await Promise.all(
    // グループIDごとにユーザーを取得する
    groupIds.map((value) => {
      return fetchGroupUsers(client, value);
    })
  ).then((fetchUsers: Array<User[]>) => { // 取得結果
    const wkUsers = fetchUsers.flatMap((value) => {
      return value;
    })
    // 重複を取り除く処理
    const result: User[] = wkUsers.filter((element, index, self) =>
                    self.findIndex(e => e.id === element.id) === index);
    return result;
  });

  logLongArray(logger, 'AllGroupUsers', 800, users.map((u) => u.id));
  if (users.length === 0) {
    // 0件の場合はここで終了
    return Promise.resolve([]);
  }

  // バッチリクエストのエントリに変換
  const usersInstalledAppsRequests: BatchRequestData[] = users.map((user) => ({
    // リクエストIDにuserIdを指定しておき、あとで使う
    id: `${user.id}`,
    method: 'GET',
    url: createInstalledAppsUrlWithAppIdFilter(user?.id ?? '', teamsAppId),
  }));

  // バッチリクエストできる粒度に分割
  const usersBatchRequestsGroup = splitArrayIntoChunks(usersInstalledAppsRequests, MAX_GRAPH_API_BATCH_COUNTS);

  // 各ユーザーに対してアプリケーションの存在を調べる
  return Promise
    .all(
      usersBatchRequestsGroup.map((requests) => {
        return fetchJsonBatch(logger, client, requests);
      })
    ).then((results: IBatchResponses[]) => (
      results
        .flatMap((result) => {
          return result?.responses ?? {} as IBatchResponseData;
        })
        .filter((result) => {
          // 200以外の結果があればロギング
          if (result.status !== 200) {
            if(result?.body?.error?.innerError) {
              logger.warn(`${result}, ${JSON.stringify(result)}`);
                }
                else{
                  logger.warn(result)
                }
          }
          // statusが200でvalueが存在する結果のみを残す
          return result.status === 200 && result.body?.value?.[0];
        })
    ));
}

/**
 * 複数ユーザーへ通知を送信する
 * @param logger
 * @param client
 * @param listItem
 * @param users
 * @param feedTitle アクティビティフィードの1行目に表示されるメッセージ
 */
export async function sendActivitiesToUsers(
  logger: Logger,
  client: Client,
  users: IBatchResponseData[],
  feedTitle: string,
): Promise<IBatchResponseData[]> {

  // リクエストbodyの共通部分を作成
  let commonBodyPart: ITeamsActivityNotificationConfig = {};
  commonBodyPart = decorateFeedOptionsWithChainId(commonBodyPart, CHAIN_ID);
  commonBodyPart = decorateFeedOptionsAsCompany(commonBodyPart, feedTitle);

  // フィード送信のリクエストを人数分作成
  const notificationRequests = users.map<BatchRequestData>((user) => ({
    id: user.id,
    method: 'POST',
    url: createSendActivityNotificationUrl(user?.id ?? ''),
    body: decorateFeedOptionsWithEntityUrl(
      commonBodyPart,
      user?.id ?? '',
      user.body?.value?.[0].id ?? '',
    ),
    headers: {
      'Content-Type': 'application/json'
    },
  } as unknown as BatchRequestData));

  // バッチリクエストできる粒度に分割
  const notificationRequestsGroup = splitArrayIntoChunks(notificationRequests, MAX_GRAPH_API_BATCH_COUNTS);

  return Promise.all(
    notificationRequestsGroup.map((requests) => {
      return fetchJsonBatch(logger, client, requests);
    })
  ).then((results: IBatchResponses[]) => (
    results
      .flatMap((result) => {
        return result?.responses ?? {} as IBatchResponseData;
      })
  ));
}
