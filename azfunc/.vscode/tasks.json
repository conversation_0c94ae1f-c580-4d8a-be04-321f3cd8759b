{"version": "2.0.0", "tasks": [{"type": "func", "label": "func: host start", "command": "host start", "problemMatcher": "$func-node-watch", "isBackground": true, "dependsOn": "npm build (functions)"}, {"type": "shell", "label": "npm build (functions)", "command": "source ~/.nvm/nvm.sh && nvm use 20 && npm run build", "dependsOn": "npm install (functions)", "problemMatcher": "$tsc", "options": {"shell": {"executable": "/bin/zsh", "args": ["-l", "-c"]}}}, {"type": "shell", "label": "npm install (functions)", "command": "source ~/.nvm/nvm.sh && nvm use 20 && npm install", "options": {"shell": {"executable": "/bin/zsh", "args": ["-l", "-c"]}}}, {"type": "shell", "label": "npm prune (functions)", "command": "npm prune --production", "dependsOn": "npm build (functions)", "problemMatcher": []}]}