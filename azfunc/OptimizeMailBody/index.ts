import { AzureFunction, Context, HttpRequest } from "@azure/functions";

const httpTrigger: AzureFunction = async function (
  context: Context,
  req: HttpRequest
): Promise<void> {
  context.log("HTTP trigger function processed a request.");
  // context.log("Request Body:", JSON.stringify(req.body));

  const records = req.body?.values ?? [];
  let over800Counter = 0;

  const processedRecords = records.map((record: any) => {
    const { recordId, data } = record;

    const decryptedText = data?.decrypted_bodyPreview ?? "";
    
    // Log Records
    context.log(`Record ${recordId}: ${decryptedText.length} characters`);
    
    // Check if text is over 800 characters
    if (decryptedText.length > 800) {
      over800Counter++;
      context.log(`- Record ${recordId} has over 800 characters, truncating...`);
    }

    const limitedText = decryptedText.substring(0, 800);
    
    return {
      recordId,
      data: {
        limitedText: limitedText,
      },
    };
  });

  // Counter Results
  context.log(`Total Records Processed: ${records.length}`);
  context.log(`Records with text over 800 characters: ${over800Counter}`);
  context.log(`Percentage over 800 chars: ${((over800Counter / records.length) * 100).toFixed(2)}%`);

  const responseBody = {
    values: processedRecords,
  };

  // context.log("Response Body:", JSON.stringify(responseBody));
  context.res = {
    status: 200,
    headers: {
      "Content-Type": "application/json",
    },
    body: responseBody,
  };
};

export default httpTrigger;