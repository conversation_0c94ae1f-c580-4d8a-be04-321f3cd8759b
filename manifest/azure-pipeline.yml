pool:
  name: Azure Pipelines
  demands: npm
  vmImage: ubuntu-22.04

# mainブランチのmanifest階層以下が更新されたときにだけトリガー
trigger:
  branches:
    include:
      - main
  paths:
    include:
      - manifest

steps:
  # manifestをzip圧縮 (local)
  - task: ArchiveFiles@2
    displayName: 'manifest/localをアーカイブ'
    inputs:
      rootFolderOrFile: 'manifest/local'
      includeRootFolder: false
      archiveFile: '$(Build.ArtifactStagingDirectory)/manifests/app_manifest_$(Build.BuildId)_local.zip'

  # manifestをzip圧縮 (develop)
  - task: ArchiveFiles@2
    displayName: 'manifest/developをアーカイブ'
    inputs:
      rootFolderOrFile: 'manifest/develop'
      includeRootFolder: false
      archiveFile: '$(Build.ArtifactStagingDirectory)/manifests/app_manifest_$(Build.BuildId)_develop.zip'

  # manifestをzip圧縮 (infra)
  - task: ArchiveFiles@2
    displayName: 'manifest/infraをアーカイブ'
    inputs:
      rootFolderOrFile: 'manifest/infra'
      includeRootFolder: false
      archiveFile: '$(Build.ArtifactStagingDirectory)/manifests/app_manifest_$(Build.BuildId)_infra.zip'

  # アプリのビルド結果をアーティファクトとしてpublish
  - task: PublishPipelineArtifact@1
    displayName: 'アプリのビルド結果をアーティファクト名manifestとしてpublish'
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/manifests'
      artifact: 'manifests'

